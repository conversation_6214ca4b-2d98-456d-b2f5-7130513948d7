package com.fxiaoke.erpdss.ipaas.base.trigger;

import com.fxiaoke.erpdss.ipaas.flow.TriggerResult;
import com.fxiaoke.erpdss.ipaas.flow.ValidationResult;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 定时触发器实现
 * 基于Quartz实现的定时任务触发器
 * 
 * <AUTHOR> (^_−)☆
 */
@Component
public class ScheduleTrigger extends BaseTrigger {

    @Autowired
    private Scheduler scheduler;

    // 存储活跃的定时任务
    private final Map<String, JobKey> activeJobs = new ConcurrentHashMap<>();

    @Override
    public String getTriggerType() {
        return "schedule";
    }

    @Override
    public String getTriggerName() {
        return "定时触发器";
    }

    @Override
    public String getTriggerDescription() {
        return "基于Cron表达式的定时任务触发器";
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        ValidationResult result = validateBaseConfig(config);
        
        try {
            // 验证必需的配置
            String cronExpression = (String) getRequiredConfigValue(config, "cronExpression");
            
            // 验证Cron表达式
            if (!isValidCronExpression(cronExpression)) {
                result.addError("Invalid cron expression: " + cronExpression);
            }
            
            // 验证时区
            String timezone = (String) getConfigValue(config, "timezone", "Asia/Shanghai");
            if (!isValidTimezone(timezone)) {
                result.addError("Invalid timezone: " + timezone);
            }
            
        } catch (IllegalArgumentException e) {
            result.addError(e.getMessage());
        }
        
        return result;
    }

    @Override
    public void start(String triggerId, Map<String, Object> config) {
        try {
            logger.info("Starting schedule trigger: {}", triggerId);
            
            // 验证配置
            ValidationResult validation = validateConfig(config);
            if (!validation.isValid()) {
                throw new IllegalArgumentException("Invalid configuration: " + validation.getErrors());
            }
            
            // 停止已存在的任务
            stop(triggerId);
            
            // 获取配置参数
            String cronExpression = (String) getRequiredConfigValue(config, "cronExpression");
            String timezone = (String) getConfigValue(config, "timezone", "Asia/Shanghai");
            String description = (String) getConfigValue(config, "description", "Schedule trigger: " + triggerId);
            
            // 创建Job
            JobDetail job = JobBuilder.newJob(ScheduleJob.class)
                .withIdentity(triggerId, "schedule-triggers")
                .withDescription(description)
                .usingJobData("triggerId", triggerId)
                .usingJobData("triggerType", getTriggerType())
                .build();
            
            // 创建Trigger
            Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity(triggerId + "-trigger", "schedule-triggers")
                .withDescription(description)
                .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression)
                    .inTimeZone(java.util.TimeZone.getTimeZone(timezone)))
                .build();
            
            // 调度任务
            scheduler.scheduleJob(job, trigger);
            activeJobs.put(triggerId, job.getKey());
            
            logger.info("Schedule trigger started successfully: {} (cron: {}, timezone: {})", 
                triggerId, cronExpression, timezone);
            
        } catch (Exception e) {
            handleError(triggerId, "start", e);
            throw new RuntimeException("Failed to start schedule trigger: " + triggerId, e);
        }
    }

    @Override
    public void stop(String triggerId) {
        try {
            logger.info("Stopping schedule trigger: {}", triggerId);
            
            JobKey jobKey = activeJobs.remove(triggerId);
            if (jobKey != null) {
                scheduler.deleteJob(jobKey);
            }
            
            logger.info("Schedule trigger stopped successfully: {}", triggerId);
            
        } catch (Exception e) {
            handleError(triggerId, "stop", e);
        }
    }

    /**
     * 验证Cron表达式
     */
    private boolean isValidCronExpression(String cronExpression) {
        try {
            CronExpression.validateExpression(cronExpression);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证时区
     */
    private boolean isValidTimezone(String timezone) {
        try {
            java.util.TimeZone.getTimeZone(timezone);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取活跃的定时任务
     */
    public Map<String, JobKey> getActiveJobs() {
        return new ConcurrentHashMap<>(activeJobs);
    }

    /**
     * Quartz Job实现
     */
    public static class ScheduleJob implements Job {
        
        @Override
        public void execute(JobExecutionContext context) throws JobExecutionException {
            JobDataMap dataMap = context.getJobDetail().getJobDataMap();
            String triggerId = dataMap.getString("triggerId");
            String triggerType = dataMap.getString("triggerType");
            
            try {
                // 获取ScheduleTrigger实例
                ScheduleTrigger scheduleTrigger = getScheduleTriggerBean(context);
                if (scheduleTrigger == null) {
                    throw new JobExecutionException("ScheduleTrigger bean not found");
                }
                
                // 构建触发数据
                Map<String, Object> triggerData = new ConcurrentHashMap<>();
                triggerData.put("scheduledTime", context.getScheduledFireTime().getTime());
                triggerData.put("actualFireTime", context.getFireTime().getTime());
                triggerData.put("jobName", context.getJobDetail().getKey().getName());
                triggerData.put("jobGroup", context.getJobDetail().getKey().getGroup());
                
                // 触发流程
                TriggerResult result = scheduleTrigger.triggerFlow(triggerId, triggerData);
                
                if (!result.isSuccess()) {
                    throw new JobExecutionException("Failed to trigger flow: " + result.getErrorMessage());
                }
                
            } catch (Exception e) {
                throw new JobExecutionException("Error executing schedule job for trigger: " + triggerId, e);
            }
        }
        
        /**
         * 从Spring上下文获取ScheduleTrigger Bean
         */
        private ScheduleTrigger getScheduleTriggerBean(JobExecutionContext context) {
            try {
                // 通过Scheduler获取ApplicationContext
                org.springframework.context.ApplicationContext applicationContext = 
                    (org.springframework.context.ApplicationContext) context.getScheduler()
                        .getContext().get("applicationContext");
                
                if (applicationContext != null) {
                    return applicationContext.getBean(ScheduleTrigger.class);
                }
                
                return null;
            } catch (Exception e) {
                return null;
            }
        }
    }
}
