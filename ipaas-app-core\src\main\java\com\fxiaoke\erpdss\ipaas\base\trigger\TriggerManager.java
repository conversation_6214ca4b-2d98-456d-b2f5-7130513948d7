package com.fxiaoke.erpdss.ipaas.base.trigger;

import com.fxiaoke.erpdss.ipaas.flow.TriggerTemplate;
import com.fxiaoke.erpdss.ipaas.flow.TriggerType;
import com.fxiaoke.erpdss.ipaas.flow.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 触发器管理器
 * 负责管理所有类型的触发器实例
 * 
 * <AUTHOR> (^_−)☆
 */
@Service
public class TriggerManager {

    private static final Logger logger = LoggerFactory.getLogger(TriggerManager.class);

    // 触发器类型映射
    private final Map<String, BaseTrigger> triggerTypes = new ConcurrentHashMap<>();
    
    // 活跃的触发器实例
    private final Map<String, TriggerInstance> activeTriggers = new ConcurrentHashMap<>();

    @Autowired
    private WebhookTrigger webhookTrigger;

    @Autowired
    private PollingTrigger pollingTrigger;

    @Autowired
    private ScheduleTrigger scheduleTrigger;

    @PostConstruct
    public void initialize() {
        logger.info("Initializing TriggerManager");
        
        // 注册触发器类型
        registerTrigger(webhookTrigger);
        registerTrigger(pollingTrigger);
        registerTrigger(scheduleTrigger);
        
        logger.info("TriggerManager initialized with {} trigger types", triggerTypes.size());
    }

    @PreDestroy
    public void shutdown() {
        logger.info("Shutting down TriggerManager");
        
        // 停止所有活跃的触发器
        activeTriggers.keySet().forEach(this::stopTrigger);
        
        // 清理轮询触发器资源
        pollingTrigger.shutdown();
        
        logger.info("TriggerManager shutdown completed");
    }

    /**
     * 注册触发器类型
     */
    private void registerTrigger(BaseTrigger trigger) {
        String type = trigger.getTriggerType();
        triggerTypes.put(type, trigger);
        logger.info("Registered trigger type: {} ({})", type, trigger.getTriggerName());
    }

    /**
     * 获取支持的触发器类型
     */
    public List<TriggerType> getSupportedTriggerTypes() {
        return triggerTypes.values().stream()
            .map(trigger -> {
                try {
                    return TriggerType.fromCode(trigger.getTriggerType());
                } catch (IllegalArgumentException e) {
                    // 如果枚举中没有定义，创建一个临时的类型描述
                    logger.warn("Trigger type not defined in enum: {}", trigger.getTriggerType());
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .toList();
    }

    /**
     * 获取触发器模板
     */
    public TriggerTemplate getTriggerTemplate(String triggerType) {
        BaseTrigger trigger = triggerTypes.get(triggerType);
        if (trigger == null) {
            throw new IllegalArgumentException("Unsupported trigger type: " + triggerType);
        }

        TriggerTemplate template = new TriggerTemplate();
        template.setType(triggerType);
        template.setName(trigger.getTriggerName());
        template.setDescription(trigger.getTriggerDescription());
        
        // 根据触发器类型设置配置字段
        template.setConfigFields(createConfigFields(triggerType));
        template.setDefaultConfig(createDefaultConfig(triggerType));
        
        return template;
    }

    /**
     * 验证触发器配置
     */
    public ValidationResult validateTriggerConfig(String triggerType, Map<String, Object> config) {
        BaseTrigger trigger = triggerTypes.get(triggerType);
        if (trigger == null) {
            ValidationResult result = new ValidationResult();
            result.addError("Unsupported trigger type: " + triggerType);
            return result;
        }
        
        return trigger.validateConfig(config);
    }

    /**
     * 启动触发器
     */
    public void startTrigger(String triggerId, String triggerType, Map<String, Object> config) {
        BaseTrigger trigger = triggerTypes.get(triggerType);
        if (trigger == null) {
            throw new IllegalArgumentException("Unsupported trigger type: " + triggerType);
        }

        // 验证配置
        ValidationResult validation = trigger.validateConfig(config);
        if (!validation.isValid()) {
            throw new IllegalArgumentException("Invalid trigger configuration: " + validation.getErrors());
        }

        // 停止已存在的触发器
        stopTrigger(triggerId);

        // 启动新的触发器
        trigger.start(triggerId, config);
        
        // 记录触发器实例
        TriggerInstance instance = new TriggerInstance(triggerId, triggerType, config, trigger);
        activeTriggers.put(triggerId, instance);
        
        logger.info("Started trigger: {} (type: {})", triggerId, triggerType);
    }

    /**
     * 停止触发器
     */
    public void stopTrigger(String triggerId) {
        TriggerInstance instance = activeTriggers.remove(triggerId);
        if (instance != null) {
            try {
                instance.getTrigger().stop(triggerId);
                logger.info("Stopped trigger: {} (type: {})", triggerId, instance.getTriggerType());
            } catch (Exception e) {
                logger.error("Error stopping trigger: " + triggerId, e);
            }
        }
    }

    /**
     * 获取活跃的触发器列表
     */
    public List<TriggerInstance> getActiveTriggers() {
        return new ArrayList<>(activeTriggers.values());
    }

    /**
     * 获取触发器实例
     */
    public TriggerInstance getTriggerInstance(String triggerId) {
        return activeTriggers.get(triggerId);
    }

    /**
     * 检查触发器是否活跃
     */
    public boolean isTriggerActive(String triggerId) {
        return activeTriggers.containsKey(triggerId);
    }

    /**
     * 创建配置字段定义
     */
    private List<TriggerTemplate.ConfigField> createConfigFields(String triggerType) {
        List<TriggerTemplate.ConfigField> fields = new ArrayList<>();
        
        switch (triggerType) {
            case "webhook":
                fields.add(createConfigField("path", "string", "Webhook路径", "Webhook接收路径", true, "/webhook"));
                fields.add(createConfigField("method", "string", "HTTP方法", "允许的HTTP方法", false, "POST"));
                break;
                
            case "polling":
                fields.add(createConfigField("url", "string", "轮询URL", "要轮询的API地址", true, null));
                fields.add(createConfigField("interval", "number", "轮询间隔", "轮询间隔时间(秒)", false, 60));
                fields.add(createConfigField("method", "string", "HTTP方法", "HTTP请求方法", false, "GET"));
                fields.add(createConfigField("detectChanges", "boolean", "检测变化", "是否只在数据变化时触发", false, true));
                break;
                
            case "schedule":
                fields.add(createConfigField("cronExpression", "string", "Cron表达式", "定时任务的Cron表达式", true, "0 0 * * * ?"));
                fields.add(createConfigField("timezone", "string", "时区", "时区设置", false, "Asia/Shanghai"));
                break;
        }
        
        return fields;
    }

    /**
     * 创建配置字段
     */
    private TriggerTemplate.ConfigField createConfigField(String name, String type, String label, 
                                                         String description, boolean required, Object defaultValue) {
        TriggerTemplate.ConfigField field = new TriggerTemplate.ConfigField();
        field.setName(name);
        field.setType(type);
        field.setLabel(label);
        field.setDescription(description);
        field.setRequired(required);
        field.setDefaultValue(defaultValue);
        return field;
    }

    /**
     * 创建默认配置
     */
    private Map<String, Object> createDefaultConfig(String triggerType) {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        
        switch (triggerType) {
            case "webhook":
                config.put("method", "POST");
                break;
            case "polling":
                config.put("interval", 60);
                config.put("method", "GET");
                config.put("detectChanges", true);
                break;
            case "schedule":
                config.put("timezone", "Asia/Shanghai");
                break;
        }
        
        return config;
    }

    /**
     * 触发器实例类
     */
    public static class TriggerInstance {
        private final String triggerId;
        private final String triggerType;
        private final Map<String, Object> config;
        private final BaseTrigger trigger;
        private final long startTime;

        public TriggerInstance(String triggerId, String triggerType, Map<String, Object> config, BaseTrigger trigger) {
            this.triggerId = triggerId;
            this.triggerType = triggerType;
            this.config = new HashMap<>(config);
            this.trigger = trigger;
            this.startTime = System.currentTimeMillis();
        }

        // Getters
        public String getTriggerId() { return triggerId; }
        public String getTriggerType() { return triggerType; }
        public Map<String, Object> getConfig() { return new HashMap<>(config); }
        public BaseTrigger getTrigger() { return trigger; }
        public long getStartTime() { return startTime; }
    }
}
