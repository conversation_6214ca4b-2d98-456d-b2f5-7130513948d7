<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke.erpdss</groupId>
        <artifactId>fs-erp-ipaas</artifactId>
        <version>2.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>ipaas-spring-common</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>i18n-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-mongodb</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.boot</groupId>
            <artifactId>actuator-ext-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.boot</groupId>
            <artifactId>metrics-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.cloud</groupId>
            <artifactId>cms-spring-cloud-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.boot</groupId>
            <artifactId>exception-handler-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.boot</groupId>
            <artifactId>http-spring-boot-starter</artifactId>
        </dependency>

        <!-- 集成测试，每个模块按需引入-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.erpdss</groupId>
            <artifactId>ipaas-api</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>
