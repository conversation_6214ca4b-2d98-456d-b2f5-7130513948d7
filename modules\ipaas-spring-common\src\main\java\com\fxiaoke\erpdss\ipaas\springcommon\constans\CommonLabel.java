package com.fxiaoke.erpdss.ipaas.springcommon.constans;

import com.fxiaoke.erpdss.ipaas.common.i18n.I18nBase;

/**
 * <AUTHOR> (^_−)☆
 */
public enum CommonLabel implements I18nBase {
    baseUrl("基础Url"),
    jsonDecodeError("JSON解码错误"),
    jsonEncodeError("JSON编码错误"),


    ;

    private final String defaultText;

    CommonLabel(String defaultText) {
        this.defaultText = defaultText;
    }

    @Override
    public String _GetI18nKey() {
        return commonI18nKeyPrefix + ".common." + name();
    }

    @Override
    public String _GetDefaultMsg() {
        return defaultText;
    }
}
