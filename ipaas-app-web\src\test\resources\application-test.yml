# iPaaS Web测试环境配置

spring:
  # 禁用不需要的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration

  # 应用配置
  application:
    name: ipaas-web-test

# 服务器配置
server:
  port: 0  # 随机端口

# iPaaS Flow Engine测试配置
ipaas:
  flow:
    engine:
      # Effektif引擎配置
      effektif:
        configuration-type: test  # 使用测试配置
        
      # 执行配置
      execution:
        timeout: 10000            # 测试环境较短的超时时间
        max-retries: 1            # 测试环境减少重试次数
        retry-delay: 1000         # 测试环境较短的重试间隔
        
      # 存储配置
      storage:
        cleanup-days: 1           # 测试环境快速清理

# OpenAPI文档配置
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: false  # 测试环境禁用UI

# 日志配置
logging:
  level:
    com.fxiaoke.erpdss.ipaas: DEBUG
    com.effektif: WARN
    org.springframework.web: WARN
    org.springframework.test: WARN
    org.quartz: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
