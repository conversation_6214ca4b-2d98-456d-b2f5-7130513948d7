package com.fxiaoke.erpdss.ipaas.base;

import com.fxiaoke.erpdss.ipaas.base.trigger.TriggerManager;
import com.fxiaoke.erpdss.ipaas.flow.TriggerTemplate;
import com.fxiaoke.erpdss.ipaas.flow.TriggerType;
import com.fxiaoke.erpdss.ipaas.flow.ValidationResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 触发器集成测试
 * 测试完整的触发器管理流程
 * 
 * <AUTHOR> (^_−)☆
 */
@SpringBootTest
@ActiveProfiles("test")
class TriggerIntegrationIT {

    @Autowired
    private TriggerManager triggerManager;

    @Test
    void testGetSupportedTriggerTypes() {
        List<TriggerType> types = triggerManager.getSupportedTriggerTypes();
        assertNotNull(types);
        assertFalse(types.isEmpty());
        
        // 验证包含基本的触发器类型
        assertTrue(types.contains(TriggerType.WEBHOOK));
        assertTrue(types.contains(TriggerType.POLLING));
        assertTrue(types.contains(TriggerType.SCHEDULE));
    }

    @Test
    void testGetWebhookTriggerTemplate() {
        TriggerTemplate template = triggerManager.getTriggerTemplate("webhook");
        assertNotNull(template);
        assertEquals("webhook", template.getType());
        assertEquals("Webhook触发器", template.getName());
        assertNotNull(template.getDescription());
        assertNotNull(template.getConfigFields());
        assertNotNull(template.getDefaultConfig());
        
        // 验证配置字段
        assertTrue(template.getConfigFields().stream()
            .anyMatch(field -> "path".equals(field.getName())));
        assertTrue(template.getConfigFields().stream()
            .anyMatch(field -> "method".equals(field.getName())));
    }

    @Test
    void testGetPollingTriggerTemplate() {
        TriggerTemplate template = triggerManager.getTriggerTemplate("polling");
        assertNotNull(template);
        assertEquals("polling", template.getType());
        assertEquals("轮询触发器", template.getName());
        
        // 验证配置字段
        assertTrue(template.getConfigFields().stream()
            .anyMatch(field -> "url".equals(field.getName())));
        assertTrue(template.getConfigFields().stream()
            .anyMatch(field -> "interval".equals(field.getName())));
    }

    @Test
    void testGetScheduleTriggerTemplate() {
        TriggerTemplate template = triggerManager.getTriggerTemplate("schedule");
        assertNotNull(template);
        assertEquals("schedule", template.getType());
        assertEquals("定时触发器", template.getName());
        
        // 验证配置字段
        assertTrue(template.getConfigFields().stream()
            .anyMatch(field -> "cronExpression".equals(field.getName())));
        assertTrue(template.getConfigFields().stream()
            .anyMatch(field -> "timezone".equals(field.getName())));
    }

    @Test
    void testValidateWebhookConfig() {
        Map<String, Object> validConfig = new HashMap<>();
        validConfig.put("path", "/test-webhook");
        validConfig.put("method", "POST");
        
        ValidationResult result = triggerManager.validateTriggerConfig("webhook", validConfig);
        assertNotNull(result);
        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
    }

    @Test
    void testValidateInvalidWebhookConfig() {
        Map<String, Object> invalidConfig = new HashMap<>();
        // 缺少必需的path字段
        
        ValidationResult result = triggerManager.validateTriggerConfig("webhook", invalidConfig);
        assertNotNull(result);
        assertFalse(result.isValid());
        assertFalse(result.getErrors().isEmpty());
    }

    @Test
    void testValidatePollingConfig() {
        Map<String, Object> validConfig = new HashMap<>();
        validConfig.put("url", "https://api.example.com/data");
        validConfig.put("interval", 30);
        validConfig.put("method", "GET");
        
        ValidationResult result = triggerManager.validateTriggerConfig("polling", validConfig);
        assertNotNull(result);
        assertTrue(result.isValid());
    }

    @Test
    void testValidateScheduleConfig() {
        Map<String, Object> validConfig = new HashMap<>();
        validConfig.put("cronExpression", "0 0 * * * ?"); // 每小时执行
        validConfig.put("timezone", "Asia/Shanghai");
        
        ValidationResult result = triggerManager.validateTriggerConfig("schedule", validConfig);
        assertNotNull(result);
        assertTrue(result.isValid());
    }

    @Test
    void testStartAndStopWebhookTrigger() {
        String triggerId = "test-webhook-trigger";
        Map<String, Object> config = new HashMap<>();
        config.put("path", "/test-webhook");
        config.put("method", "POST");
        
        // 启动触发器
        assertDoesNotThrow(() -> {
            triggerManager.startTrigger(triggerId, "webhook", config);
        });
        
        // 验证触发器已启动
        assertTrue(triggerManager.isTriggerActive(triggerId));
        assertNotNull(triggerManager.getTriggerInstance(triggerId));
        
        // 验证活跃触发器列表
        List<TriggerManager.TriggerInstance> activeTriggers = triggerManager.getActiveTriggers();
        assertTrue(activeTriggers.stream()
            .anyMatch(instance -> triggerId.equals(instance.getTriggerId())));
        
        // 停止触发器
        triggerManager.stopTrigger(triggerId);
        
        // 验证触发器已停止
        assertFalse(triggerManager.isTriggerActive(triggerId));
        assertNull(triggerManager.getTriggerInstance(triggerId));
    }

    @Test
    void testStartTriggerWithInvalidConfig() {
        String triggerId = "test-invalid-trigger";
        Map<String, Object> invalidConfig = new HashMap<>();
        // 缺少必需的配置
        
        assertThrows(IllegalArgumentException.class, () -> {
            triggerManager.startTrigger(triggerId, "webhook", invalidConfig);
        });
        
        // 验证触发器未启动
        assertFalse(triggerManager.isTriggerActive(triggerId));
    }

    @Test
    void testStartTriggerWithUnsupportedType() {
        String triggerId = "test-unsupported-trigger";
        Map<String, Object> config = new HashMap<>();
        
        assertThrows(IllegalArgumentException.class, () -> {
            triggerManager.startTrigger(triggerId, "unsupported-type", config);
        });
    }

    @Test
    void testMultipleTriggers() {
        String webhookTriggerId = "webhook-trigger";
        String scheduleTriggerId = "schedule-trigger";
        
        // 启动webhook触发器
        Map<String, Object> webhookConfig = new HashMap<>();
        webhookConfig.put("path", "/webhook");
        webhookConfig.put("method", "POST");
        triggerManager.startTrigger(webhookTriggerId, "webhook", webhookConfig);
        
        // 启动定时触发器
        Map<String, Object> scheduleConfig = new HashMap<>();
        scheduleConfig.put("cronExpression", "0 0 * * * ?");
        scheduleConfig.put("timezone", "Asia/Shanghai");
        triggerManager.startTrigger(scheduleTriggerId, "schedule", scheduleConfig);
        
        // 验证两个触发器都已启动
        assertTrue(triggerManager.isTriggerActive(webhookTriggerId));
        assertTrue(triggerManager.isTriggerActive(scheduleTriggerId));
        
        List<TriggerManager.TriggerInstance> activeTriggers = triggerManager.getActiveTriggers();
        assertEquals(2, activeTriggers.size());
        
        // 停止所有触发器
        triggerManager.stopTrigger(webhookTriggerId);
        triggerManager.stopTrigger(scheduleTriggerId);
        
        // 验证所有触发器都已停止
        assertFalse(triggerManager.isTriggerActive(webhookTriggerId));
        assertFalse(triggerManager.isTriggerActive(scheduleTriggerId));
        assertTrue(triggerManager.getActiveTriggers().isEmpty());
    }
}
