package com.fxiaoke.erpdss.ipaas.base.trigger;

import com.fxiaoke.erpdss.ipaas.flow.TriggerResult;
import com.fxiaoke.erpdss.ipaas.flow.ValidationResult;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Webhook 触发器实现
 * 通过HTTP接口接收外部系统的webhook调用
 * 
 * <AUTHOR> (^_−)☆
 */
@Component
@RestController
@RequestMapping("/api/triggers/webhook")
public class WebhookTrigger extends BaseTrigger {

    // 存储活跃的webhook配置
    private final Map<String, Map<String, Object>> activeWebhooks = new ConcurrentHashMap<>();

    @Override
    public String getTriggerType() {
        return "webhook";
    }

    @Override
    public String getTriggerName() {
        return "Webhook触发器";
    }

    @Override
    public String getTriggerDescription() {
        return "通过HTTP Webhook接收外部系统调用来触发流程";
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        ValidationResult result = validateBaseConfig(config);
        
        try {
            // 验证必需的配置
            getRequiredConfigValue(config, "path");
            
            // 验证HTTP方法
            String method = (String) getConfigValue(config, "method", "POST");
            if (!isValidHttpMethod(method)) {
                result.addError("Invalid HTTP method: " + method);
            }
            
            // 验证路径格式
            String path = (String) config.get("path");
            if (!isValidPath(path)) {
                result.addError("Invalid webhook path: " + path);
            }
            
        } catch (IllegalArgumentException e) {
            result.addError(e.getMessage());
        }
        
        return result;
    }

    @Override
    public void start(String triggerId, Map<String, Object> config) {
        try {
            logger.info("Starting webhook trigger: {}", triggerId);
            
            // 验证配置
            ValidationResult validation = validateConfig(config);
            if (!validation.isValid()) {
                throw new IllegalArgumentException("Invalid configuration: " + validation.getErrors());
            }
            
            // 存储配置
            activeWebhooks.put(triggerId, config);
            
            logger.info("Webhook trigger started successfully: {}", triggerId);
            
        } catch (Exception e) {
            handleError(triggerId, "start", e);
            throw new RuntimeException("Failed to start webhook trigger: " + triggerId, e);
        }
    }

    @Override
    public void stop(String triggerId) {
        try {
            logger.info("Stopping webhook trigger: {}", triggerId);
            
            activeWebhooks.remove(triggerId);
            
            logger.info("Webhook trigger stopped successfully: {}", triggerId);
            
        } catch (Exception e) {
            handleError(triggerId, "stop", e);
        }
    }

    /**
     * 处理 POST 请求的 webhook
     */
    @PostMapping("/{triggerId}")
    public TriggerResult handlePostWebhook(
            @PathVariable String triggerId,
            @RequestBody(required = false) Map<String, Object> payload,
            @RequestParam Map<String, String> queryParams,
            @RequestHeader Map<String, String> headers) {
        
        return handleWebhookRequest(triggerId, "POST", payload, queryParams, headers);
    }

    /**
     * 处理 GET 请求的 webhook
     */
    @GetMapping("/{triggerId}")
    public TriggerResult handleGetWebhook(
            @PathVariable String triggerId,
            @RequestParam Map<String, String> queryParams,
            @RequestHeader Map<String, String> headers) {
        
        return handleWebhookRequest(triggerId, "GET", null, queryParams, headers);
    }

    /**
     * 处理 PUT 请求的 webhook
     */
    @PutMapping("/{triggerId}")
    public TriggerResult handlePutWebhook(
            @PathVariable String triggerId,
            @RequestBody(required = false) Map<String, Object> payload,
            @RequestParam Map<String, String> queryParams,
            @RequestHeader Map<String, String> headers) {
        
        return handleWebhookRequest(triggerId, "PUT", payload, queryParams, headers);
    }

    /**
     * 处理 DELETE 请求的 webhook
     */
    @DeleteMapping("/{triggerId}")
    public TriggerResult handleDeleteWebhook(
            @PathVariable String triggerId,
            @RequestParam Map<String, String> queryParams,
            @RequestHeader Map<String, String> headers) {
        
        return handleWebhookRequest(triggerId, "DELETE", null, queryParams, headers);
    }

    /**
     * 统一处理 webhook 请求
     */
    private TriggerResult handleWebhookRequest(
            String triggerId,
            String method,
            Map<String, Object> payload,
            Map<String, String> queryParams,
            Map<String, String> headers) {
        
        try {
            logger.info("Received {} webhook request for trigger: {}", method, triggerId);
            
            // 检查触发器是否存在且活跃
            Map<String, Object> config = activeWebhooks.get(triggerId);
            if (config == null) {
                logger.warn("Webhook trigger not found or not active: {}", triggerId);
                return TriggerResult.failure(triggerId, "Webhook trigger not found or not active");
            }
            
            // 验证HTTP方法
            String allowedMethod = (String) getConfigValue(config, "method", "POST");
            if (!method.equalsIgnoreCase(allowedMethod)) {
                logger.warn("HTTP method not allowed for trigger {}: {} (expected: {})", 
                    triggerId, method, allowedMethod);
                return TriggerResult.failure(triggerId, "HTTP method not allowed: " + method);
            }
            
            // 构建触发数据
            Map<String, Object> triggerData = new ConcurrentHashMap<>();
            triggerData.put("method", method);
            triggerData.put("headers", headers);
            triggerData.put("queryParams", queryParams);
            if (payload != null) {
                triggerData.put("payload", payload);
            }
            
            // 触发流程
            return triggerFlow(triggerId, triggerData);
            
        } catch (Exception e) {
            logger.error("Error handling webhook request for trigger: " + triggerId, e);
            return TriggerResult.failure(triggerId, "Error processing webhook: " + e.getMessage());
        }
    }

    /**
     * 验证HTTP方法
     */
    private boolean isValidHttpMethod(String method) {
        return method != null && 
               (method.equalsIgnoreCase("GET") || 
                method.equalsIgnoreCase("POST") || 
                method.equalsIgnoreCase("PUT") || 
                method.equalsIgnoreCase("DELETE"));
    }

    /**
     * 验证路径格式
     */
    private boolean isValidPath(String path) {
        return path != null && 
               path.startsWith("/") && 
               path.length() > 1 && 
               !path.contains("..") &&
               path.matches("^[a-zA-Z0-9/_-]+$");
    }

    /**
     * 获取活跃的webhook列表
     */
    public Map<String, Map<String, Object>> getActiveWebhooks() {
        return new ConcurrentHashMap<>(activeWebhooks);
    }
}
