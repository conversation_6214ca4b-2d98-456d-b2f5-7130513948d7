# iPaaS 流程引擎 (ipaas-flow-engine) 开发文档

## 1. 模块概述

iPaaS 流程引擎模块是基于 Effektif 工作流引擎的核心服务层实现，提供类似 Zapier 的自动化流程能力。本模块专注于业务逻辑实现，通过
API 接口为 web 和 base 模块提供服务。

### 1.1 架构定位

- **服务层**：只实现 service 方法，不包含 web 控制器或具体触发器实现
- **API 提供者**：为 web 和 base 模块提供标准化的流程管理 API
- **Effektif 集成**：深度集成 Effektif 工作流引擎，利用其完整的生命周期管理和原生tenantId支持
- **触发器抽象**：只实现 BaseTrigger，接收数据组装后启动流程

### 1.2 核心功能

- 流程定义与管理服务（基于 Effektif ExecutableWorkflow）
- 基础触发器服务（数据接收和流程启动）
- 动作执行与编排服务（基于 Effektif ActivityType）
- 流程执行与状态管理服务（基于 Effektif WorkflowInstance）
- 执行历史记录与查询服务

### 1.3 技术栈

- Java 21
- Spring Boot 3.x
- Effektif 工作流引擎 (effektif-workflow-api, effektif-workflow-impl)
- MongoDB (用于存储流程定义和执行历史)
- Spring Data MongoDB
- Effektif Memory/MongoDB 存储策略

## 2. 架构设计

### 2.1 基于 Effektif 的分层架构

本架构深度集成 Effektif 工作流引擎，利用其完整的生命周期管理、存储抽象和数据处理机制。

```plantuml
@startuml
package "ipaas-web" {
  [FlowManagementController 集成流管理] as FlowManagementController
  [TriggerManagementController] as TriggerManagementController
  [TriggerFeignClient]
}

package "ipaas-core" {
  [TriggerController]
  [TriggerRpcController]
  frame "trigger实现"{
  [PollingTriggerImpl] as PollingTrigger
  [WebhookTriggerImpl] as WebhookTrigger
  [ScheduleTriggerImpl] as ScheduleTrigger
  }
}

package "ipaas-api" {
  [FlowService]
  [TriggerService]
  [TriggerRpcService]
}

package "ipaas-flow-engine" {
  [FlowServiceImpl] as FlowServiceImpl
  [FlowExecutionService] as ExecutionServiceImpl
  [ActionService] as ActionServiceImpl
  [EffektifAdapter] as EffektifAdapter
}

package "effektif-workflow-api" {
  [WorkflowEngine] as EffektifEngine
  [ExecutableWorkflow] as EffektifWorkflow
  [TriggerInstance] as EffektifTrigger
  [WorkflowInstance] as EffektifInstance
  [DataContainer] as EffektifDataContainer
}

package "effektif-workflow-impl" {
  [WorkflowEngineImpl] as EffektifEngineImpl
  [WorkflowInstanceImpl] as EffektifInstanceImpl
  [MemoryConfiguration] as MemoryConfig
  [MongoConfiguration] as MongoConfig
  frame "Memory存储层" {
    [MemoryWorkflowStore]
    [MemoryWorkflowInstanceStore]
    [MemoryJobStore]
    [MemoryTaskStore]
  }
}

database "MongoDB" {
  [FlowRepository] as FlowRepo
  [ExecutionRepository] as ExecRepo
  [EffektifWorkflowStore] as EffektifWorkflowRepo
  [EffektifInstanceStore] as EffektifInstanceRepo
}

[TriggerFeignClient] ..> [TriggerRpcService] : 实现
[TriggerRpcController] ..> [TriggerRpcService] : 实现
[FlowServiceImpl] ..> [FlowService] : 实现

FlowManagementController --> FlowService : Jar API调用
TriggerManagementController -->TriggerFeignClient : 调用
TriggerFeignClient --> [TriggerRpcController] : RPC API调用

PollingTrigger ..> TriggerService : 实现
WebhookTrigger ..> TriggerService : 实现
ScheduleTrigger ..> TriggerService : 实现

FlowServiceImpl --> EffektifAdapter : Effektif集成
EffektifAdapter --> EffektifEngine : Effektif API调用
ExecutionServiceImpl --> EffektifAdapter : 执行管理
TriggerService --> ExecutionServiceImpl : 启动流程

EffektifEngine --> EffektifEngineImpl : 实现
EffektifEngineImpl --> MemoryConfig : 开发/测试环境
EffektifEngineImpl --> MongoConfig : 生产环境

FlowServiceImpl --> FlowRepo : 集成流配置存储
ExecutionServiceImpl --> ExecRepo : 执行历史
EffektifEngineImpl --> EffektifWorkflowRepo : 工作流存储
EffektifEngineImpl --> EffektifInstanceRepo : 实例存储
@enduml
```

### 2.2 核心服务组件

基于 Effektif 工作流引擎的核心组件设计：

1. **FlowService**：流程定义的创建、更新、发布和状态管理服务（基于 Effektif ExecutableWorkflow）
2. **BaseTriggerService**：基础触发器服务，接收触发数据并启动流程（基于 Effektif TriggerInstance）
3. **ActionService**：动作配置和执行服务（基于 Effektif ActivityType）
4. **FlowExecutionService**：流程实例的创建和执行管理服务（基于 Effektif WorkflowInstance）
5. **EffektifAdapter**：Effektif API 适配器，处理数据转换和集成
6. **ExecutionHistoryService**：执行历史记录和查询服务

### 2.3 Effektif 集成层次

#### 2.3.1 存储层集成

- **开发/测试环境**：使用 Effektif MemoryConfiguration，支持快速开发和测试
- **生产环境**：使用 Effektif MongoConfiguration，提供持久化和分布式支持
- **混合模式**：MongoMemoryConfiguration，工作流定义用内存，实例用 MongoDB

#### 2.3.2 数据处理层集成

- **持久化数据**：利用 DataContainer.data 存储业务关键数据和跨节点传递数据
- **临时数据**：利用 DataContainer.transientData 存储运行时上下文和敏感信息

#### 2.3.3 生命周期集成

- **工作流部署**：基于 Effektif 的解析、验证、存储和缓存机制
- **实例执行**：利用 Effektif 的锁定、异步执行和状态管理
- **错误处理**：基于 Effektif 的异常状态和恢复机制

### 2.4 模块职责分工

- **ipaas-flow-engine**：核心业务逻辑，Service 层实现，深度集成 Effektif API
- **ipaas-web**：Web API 层，调用 flow-engine 的服务
- **ipaas-core**：具体触发器实现（轮询、Webhook等），调用 flow-engine 的 BaseTriggerService
- **effektif-workflow-impl**：提供工作流引擎的底层实现和存储抽象

## 3. 核心领域模型

### 3.1 基于 Effektif 的领域模型

基于 Effektif DataContainer 和 ExecutableWorkflow 的领域模型：

```plantuml
@startuml
enum FlowStatus {
  DRAFT
  PUBLISHED
  RUNNING
  PAUSED
  STOPPED
  ARCHIVED
}

enum ExecutionStatus {
  PENDING
  RUNNING
  SUCCESS
  FAILED
  CANCELLED
  TIMEOUT
  RETRYING
}

class Flow {
  +String id
  +String name
  +String description
  +FlowStatus status
  +Trigger trigger
  +List<Action> actions
  +Map<String, Object> config
  +List<String> tags
  +String category
  +int version
  +Date createdAt
  +Date updatedAt
  +String createdBy
  +String updatedBy
  --基于 Effektif ExecutableWorkflow--
  +ExecutableWorkflow toExecutableWorkflow()
  +static Flow fromExecutableWorkflow(ExecutableWorkflow)
}

class Trigger {
  +String id
  +String name
  +String type
  +Map<String, Object> config
  +boolean enabled
  +String description
}

class Action {
  +String id
  +String name
  +String type
  +int order
  +Map<String, Object> config
  +String condition
  +boolean enabled
  +String errorHandling
  +Map<String, Object> inputMapping
  +Map<String, Object> outputMapping
}

class FlowExecution {
  +String id
  +String flowId
  +int flowVersion
  +ExecutionStatus status
  +Map<String, Object> triggerData
  +Map<String, Object> contextData
  +List<ActionExecution> actionExecutions
  +Date startTime
  +Date endTime
  +long duration
  +String errorMessage
  +String environment
  +String executedBy
}

class ActionExecution {
  +String id
  +String actionId
  +String name
  +ExecutionStatus status
  +Map<String, Object> input
  +Map<String, Object> output
  +Date startTime
  +Date endTime
  +long duration
  +String errorMessage
  +int retryCount
}

note right of Flow
  直接使用 Effektif TriggerInstance
  TriggerInstance 本身已包含 tenantId 字段
  继承自 DataContainer，支持 data/transientData
end note

Flow "1" *-- "1" Trigger
Flow "1" *-- "n" Action
FlowExecution "n" -- "1" Flow
FlowExecution "1" *-- "n" ActionExecution
@enduml
```

### 3.2 与 Effektif API 的映射关系

```plantuml
@startuml
package "iPaaS Models" {
  class Flow
  class FlowExecution
}

package "Effektif API Models" {
  class ExecutableWorkflow
  class TriggerInstance
  class WorkflowInstance
  class DataContainer
}

package "Effektif Impl Models" {
  class WorkflowInstanceImpl
  class ActivityInstanceImpl
}

Flow --> ExecutableWorkflow : 转换映射
FlowExecution --> WorkflowInstance : 基于WorkflowInstance
FlowExecution --> WorkflowInstanceImpl : 实例管理

note right of Flow
  基于 ExecutableWorkflow
  保持 Effektif 生命周期
end note

note right of TriggerInstance
  Effektif 原生支持多租户
  内置 tenantId 字段
  继承 DataContainer
  data: 持久化业务数据
  transientData: 临时运行时数据
end note

note right of FlowExecution
  基于 WorkflowInstance
  利用 Effektif 状态管理
  支持锁定和并发控制
end note
@enduml
```

## 4. 基于 Effektif 的流程状态流转

### 4.1 流程定义状态流转（iPaaS 层）

```plantuml
@startuml
[*] --> DRAFT
DRAFT --> PUBLISHED : 发布到Effektif
PUBLISHED --> RUNNING : 启动
RUNNING --> PAUSED : 暂停
PAUSED --> RUNNING : 恢复
RUNNING --> STOPPED : 停止
PUBLISHED --> STOPPED : 停止
STOPPED --> ARCHIVED : 归档
RUNNING --> ARCHIVED : 归档

note right of PUBLISHED
  调用 Effektif
  deployWorkflow()
  缓存到 WorkflowCache
end note
@enduml
```

### 4.2 工作流实例状态流转（基于 Effektif）

```plantuml
@startuml
[*] --> PENDING
PENDING --> RUNNING : Effektif.start()
RUNNING --> SUCCESS : 正常结束
RUNNING --> FAILED : 执行失败
RUNNING --> CANCELLED : cancel()
RUNNING --> TIMEOUT : 执行超时
FAILED --> RETRYING : 重试机制
RETRYING --> RUNNING : 重新执行

note right of RUNNING
  Effektif WorkflowInstance
  支持锁定机制
  异步/同步执行
  状态持久化
end note

note right of SUCCESS
  endAndPropagateToParent()
  workflowInstanceEnded()
  资源清理
end note
@enduml
```

### 4.3 Effektif 活动实例状态

```plantuml
@startuml
[*] --> starting
starting --> executing : execute()
executing --> propagateToParent : 完成
executing --> joining : 等待合并
joining --> propagateToParent : 合并完成
propagateToParent --> [*] : end()

note right of starting
  STATE_STARTING
  STATE_STARTING_MULTI_INSTANCE
  STATE_STARTING_MULTI_CONTAINER
end note

note right of executing
  活动具体执行逻辑
  可能产生异步工作
end note
@enduml
```

## 5. 基于 Effektif 的核心服务流程

### 5.1 流程创建与发布流程（深度集成 Effektif）

```plantuml
@startuml
participant "WebController" as WC
participant "FlowService" as FS
participant "EffektifAdapter" as EA
participant "FlowRepository" as FR
participant "EffektifEngine" as EE
participant "WorkflowCache" as WC_Cache

WC -> FS : createFlow(tenantId, flowDTO)
FS -> FS : 验证流程定义
FS -> EA : createTenantFlow(tenantId, flow)
EA -> EA : 包装tenantId到properties
EA -> FR : save(tenantFlow)
FR --> EA : flowId
EA --> FS : 返回flowId
FS --> WC : 返回创建结果

WC -> FS : publishFlow(tenantId, flowId)
FS -> FR : findByTenantIdAndId(tenantId, flowId)
FR --> FS : tenantFlow
FS -> FS : 验证流程完整性
FS -> EA : convertToExecutableWorkflow(tenantFlow)
EA -> EA : 转换为Effektif ExecutableWorkflow
EA -> EA : 设置tenantId到workflow.properties
EA -> EE : deployWorkflow(executableWorkflow)
EE -> EE : WorkflowParser.parse()
EE -> EE : workflowStore.insertWorkflow()
EE -> WC_Cache : workflowCache.put(workflowImpl)
EE --> EA : deployment
EA -> FR : updateStatus(tenantId, flowId, PUBLISHED)
EA --> FS : 返回发布结果
FS --> WC : 返回发布结果

note right of EE
  Effektif 完整部署流程：
  1. 解析和验证
  2. 生成WorkflowId
  3. 存储到WorkflowStore
  4. 缓存WorkflowImpl
  5. 触发器发布通知
end note
@enduml
```

### 5.2 基础触发器服务流程

```plantuml
@startuml
participant "BaseTriggerImpl" as BTI
participant "BaseTriggerService" as BTS
participant "EffektifAdapter" as EA
participant "EffektifEngine" as EE
participant "WorkflowInstanceStore" as WIS

note over BTI
  具体实现在 ipaas-core 模块
  如：PollingTriggerImpl
      WebhookTriggerImpl
      ScheduleTriggerImpl
end note

BTI -> BTS : triggerFlow(triggerId, triggerData)
BTS -> BTS : 验证触发器配置
BTS -> EA : findFlowByTriggerId(triggerId)
EA --> BTS : flow
BTS -> EA : createTriggerInstance(triggerData)
EA -> EA : 创建Effektif TriggerInstance
EA -> EA : 设置data字段（持久化业务数据）
EA -> EA : 设置transientData字段（临时运行时数据）
EA -> EE : start(triggerInstance)

EE -> EE : startInitialize(triggerInstance)
EE -> EE : 创建WorkflowInstanceImpl
EE -> EE : 设置锁定机制
EE -> EE : 应用触发器数据到变量
EE -> EE : startExecute(workflowInstance)
EE -> EE : executeWork() - 执行活动实例
EE -> WIS : insertWorkflowInstance()
EE --> EA : workflowInstance

EA -> BTS : recordExecution(execution)
BTS --> BTI : 返回执行结果

note right of EE
  Effektif 完整启动流程：
  1. 实例初始化和锁定
  2. 触发器数据应用
  3. 启动活动执行
  4. 异步/同步工作处理
  5. 状态持久化
end note
@enduml
```



## 6. 基于 Effektif 的服务接口设计

### 6.1 FlowService 接口（集成 Effektif API）

```java
@Service
public interface FlowService {
    // 流程管理 - 基于 Effektif ExecutableWorkflow
    Flow createFlow(CreateFlowRequest request);

    Flow updateFlow(String flowId, UpdateFlowRequest request);

    Flow getFlow(String flowId);

    List<Flow> queryFlows(FlowQueryRequest request);

    // 流程状态管理 - 集成 Effektif 部署和生命周期
    Flow publishFlow(String flowId);  // 调用 Effektif.deployWorkflow()

    Flow startFlow(String flowId);    // 启用工作流

    Flow pauseFlow(String flowId);    // 暂停工作流

    Flow stopFlow(String flowId);     // 停止工作流

    Flow archiveFlow(String flowId);  // 归档工作流

    // 流程验证 - 利用 Effektif WorkflowParser
    ValidationResult validateFlow(Flow flow);

    // Effektif 集成方法
    ExecutableWorkflow convertToExecutableWorkflow(Flow flow);

    Flow convertFromExecutableWorkflow(ExecutableWorkflow workflow);
}
```

### 6.2 BaseTriggerService 接口（基于 Effektif TriggerInstance）

```java
@Service
public interface BaseTriggerService {
    // 基础触发器方法 - 基于 Effektif TriggerInstance
    TriggerResult triggerFlow(String triggerId, Map<String, Object> triggerData);

    TriggerResult triggerFlowByWorkflowId(String workflowId, Map<String, Object> triggerData);

    // 触发器配置
    List<TriggerType> getSupportedTriggerTypes();

    TriggerTemplate getTriggerTemplate(String triggerType);

    ValidationResult validateTriggerConfig(String triggerType, Map<String, Object> config);

    // Effektif TriggerInstance 创建
    TriggerInstance createTriggerInstance(String workflowId, Map<String, Object> triggerData);

    // 数据处理方法 - 利用 TriggerInstance 继承的 DataContainer data/transientData
    void setBusinessData(TriggerInstance triggerInstance, Map<String, Object> businessData);

    void setRuntimeData(TriggerInstance triggerInstance, Map<String, Object> runtimeData);

    Map<String, Object> extractBusinessData(TriggerInstance triggerInstance);

    Map<String, Object> extractRuntimeData(TriggerInstance triggerInstance);
}
```

### 6.3 FlowExecutionService 接口

```java

@Service
public interface FlowExecutionService {
    // 流程执行
    TenantFlowExecution executeFlow(String tenantId, String flowId, Map<String, Object> triggerData);

    TenantFlowExecution getExecution(String tenantId, String executionId);

    List<TenantFlowExecution> queryExecutions(String tenantId, ExecutionQueryRequest request);

    // 执行控制
    TenantFlowExecution cancelExecution(String tenantId, String executionId);

    TenantFlowExecution retryExecution(String tenantId, String executionId);

    // 执行历史
    void recordExecution(String tenantId, TenantFlowExecution execution);

    void cleanupExecutions(String tenantId, int retentionDays);
}
```

### 6.4 TenantDataProcessor 接口（基于 Effektif DataContainer）

```java
@Service
public interface TenantDataProcessor {
    // 数据处理 - 基于 Effektif DataContainer data/transientData 机制
    void processIncomingData(String tenantId, DataContainer container, Map<String, Object> incomingData,
                           DataProcessingConfig config);

    void separateBusinessAndRuntimeData(Map<String, Object> sourceData,
                                      Map<String, Object> businessData,
                                      Map<String, Object> runtimeData,
                                      DataClassificationConfig config);

    // 多租户数据隔离
    void applyTenantIsolation(String tenantId, DataContainer container);

    Map<String, Object> extractTenantBusinessData(String tenantId, DataContainer container);

    Map<String, Object> extractTenantRuntimeData(String tenantId, DataContainer container);

    // 数据验证和转换
    ValidationResult validateTenantData(String tenantId, Map<String, Object> data,
                                      TenantDataValidationConfig config);

    Map<String, Object> transformTenantData(String tenantId, Map<String, Object> sourceData,
                                          DataTransformationConfig config);

    // 敏感数据处理
    void handleSensitiveData(String tenantId, DataContainer container,
                           Map<String, Object> sensitiveData, SecurityConfig config);

    void cleanupSensitiveData(String tenantId, DataContainer container, SecurityConfig config);
}
```

## 7. 触发器架构设计

### 7.1 触发器分层实现

```plantuml
@startuml
package "ipaas-core (具体实现)" {
  class PollingTriggerImpl {
    +void startPolling()
    +void stopPolling()
    +void onPollingEvent(data)
  }

  class WebhookTriggerImpl {
    +void registerWebhook()
    +void unregisterWebhook()
    +void onWebhookReceived(data)
  }

  class ScheduleTriggerImpl {
    +void scheduleTask()
    +void cancelTask()
    +void onScheduleTriggered(data)
  }
}

package "ipaas-flow-engine (抽象服务)" {
  interface BaseTriggerService {
    +triggerFlow(tenantId, triggerId, data)
  }

  class BaseTriggerServiceImpl {
    +triggerFlow(tenantId, triggerId, data)
    +validateTriggerData(data)
    +startFlowExecution(tenantId, flowId, data)
  }
}

PollingTriggerImpl --> BaseTriggerService : 调用
WebhookTriggerImpl --> BaseTriggerService : 调用
ScheduleTriggerImpl --> BaseTriggerService : 调用

note right of BaseTriggerService
  只负责接收触发数据
  组装后启动流程
  不包含具体触发逻辑
end note

note left of PollingTriggerImpl
  具体的轮询实现
  定时检查数据源
  发现变化时调用BaseTriggerService
end note
@enduml
```

### 7.2 触发器类型定义

```java
public enum TriggerType {
    POLLING("polling", "轮询触发器"),
    WEBHOOK("webhook", "Webhook触发器"),
    SCHEDULE("schedule", "定时触发器"),
    MESSAGE_QUEUE("messageQueue", "消息队列触发器"),
    DATABASE("database", "数据库触发器"),
    FILE_SYSTEM("fileSystem", "文件系统触发器");

    private final String code;
    private final String description;
}

public class TriggerTemplate {
    private String type;
    private String name;
    private String description;
    private List<ConfigField> configFields;
    private Map<String, Object> defaultConfig;
}

public class TriggerResult {
    private String tenantId;
    private String triggerId;
    private String executionId;
    private boolean success;
    private String errorMessage;
    private Map<String, Object> resultData;
}
```

## 8. 动作服务设计

### 8.1 ActionService 接口

```java

@Service
public interface ActionService {
    // 动作类型管理
    List<ActionType> getSupportedActionTypes();

    ActionTemplate getActionTemplate(String actionType);

    ValidationResult validateActionConfig(String actionType, Map<String, Object> config);

    // 动作执行
    ActionResult executeAction(String tenantId, TenantAction action, Map<String, Object> input);

    ActionResult testAction(String tenantId, TenantAction action, Map<String, Object> testInput);

    // 动作编排
    List<ActionResult> executeActionChain(String tenantId, List<TenantAction> actions, Map<String, Object> initialInput);
}
```

## 9. 深度集成 Effektif 工作流引擎

### 9.1 完整集成架构

```plantuml
@startuml
package "iPaaS Flow Engine" {
  [FlowService] as FS
  [FlowExecutionService] as FES
  [TenantDataProcessor] as TDP
  [EffektifAdapter] as EA
}

package "Effektif API" {
  [WorkflowEngine] as WE
  [ExecutableWorkflow] as EW
  [WorkflowInstance] as WI
  [TriggerInstance] as TI
  [DataContainer] as DC
}

package "Effektif Implementation" {
  [WorkflowEngineImpl] as WEI
  [WorkflowInstanceImpl] as WII
  [ActivityInstanceImpl] as AII
  [MemoryConfiguration] as MC
  [MongoConfiguration] as MoC
}

package "Effektif Storage" {
  [WorkflowStore] as WS
  [WorkflowInstanceStore] as WIS
  [JobStore] as JS
  [TaskStore] as TS
}

FS --> EA : 流程管理
FES --> EA : 执行管理
TDP --> DC : 数据处理
EA --> WE : Effektif API调用

WE --> WEI : 实现
WEI --> MC : 开发/测试
WEI --> MoC : 生产环境
WEI --> WS : 工作流存储
WEI --> WIS : 实例存储
WEI --> JS : 作业存储
WEI --> TS : 任务存储

EA --> TI : 创建触发实例
EA --> WE : 启动工作流
WE --> WI : 执行实例
WI --> WII : 实例实现
WII --> AII : 活动实例
WII --> EA : 执行状态回调
EA --> FES : 更新执行状态

note right of EA
  EffektifAdapter 职责：
  1. iPaaS模型与Effektif API转换
  2. 多租户数据包装
  3. DataContainer数据处理
  4. 生命周期管理集成
end note

note right of TDP
  TenantDataProcessor 职责：
  1. 利用data/transientData机制
  2. 业务数据持久化
  3. 运行时数据临时存储
  4. 敏感数据安全处理
end note
@enduml
```

### 9.2 基于 Effektif 的模型映射关系

iPaaS 多租户模型与 Effektif API 的深度集成映射：

1. **TenantFlow → ExecutableWorkflow + Effektif 生命周期**

   - 添加 tenantId 到 workflow properties
   - 转换触发器和动作定义
   - 利用 Effektif 部署和缓存机制
   - 保持 Effektif 版本和状态映射
2. **直接使用 Effektif TriggerInstance（原生多租户支持）**

   - TriggerInstance 内置 tenantId 字段，无需额外包装
   - 继承 DataContainer，支持 data/transientData 机制
   - 业务数据存储到 data 字段（持久化）
   - 运行时数据存储到 transientData 字段（临时）
   - 敏感信息仅存储到 transientData 字段
   - 维护业务键映射
3. **TenantFlowExecution → WorkflowInstance + WorkflowInstanceImpl**

   - 记录租户执行信息
   - 利用 Effektif 锁定和并发控制
   - 基于 Effektif 状态管理和转换
   - 支持 Effektif 异步/同步执行模式
   - 执行历史追踪和生命周期管理

### 9.3 EffektifAdapter 实现（深度集成 DataContainer）

```java
@Component
public class EffektifAdapter {

    @Autowired
    private WorkflowEngine workflowEngine;

    @Autowired
    private TenantDataProcessor tenantDataProcessor;

    // 流程定义转换 - 基于 Effektif ExecutableWorkflow
    public ExecutableWorkflow convertToExecutableWorkflow(TenantFlow tenantFlow) {
        ExecutableWorkflow workflow = new ExecutableWorkflow();
        workflow.setId(tenantFlow.getId());
        workflow.setName(tenantFlow.getName());
        workflow.setDescription(tenantFlow.getDescription());

        // 添加租户信息到属性中 - 利用 Effektif properties 机制
        workflow.property("tenantId", tenantFlow.getTenantId());
        workflow.property("iPaaSFlowId", tenantFlow.getId());
        workflow.property("iPaaSVersion", tenantFlow.getVersion());

        // 转换触发器和动作
        convertTriggerAndActions(tenantFlow, workflow);

        return workflow;
    }

    // 触发实例创建 - 直接使用 Effektif TriggerInstance（内置 tenantId 支持）
    public TriggerInstance createTriggerInstance(String tenantId, String workflowId,
                                               Map<String, Object> triggerData,
                                               DataProcessingConfig config) {
        // TriggerInstance 构造函数直接支持 tenantId
        TriggerInstance triggerInstance = new TriggerInstance(tenantId);
        triggerInstance.sourceWorkflowId(workflowId);

        // 利用 TenantDataProcessor 处理数据到 DataContainer
        tenantDataProcessor.processIncomingData(tenantId, triggerInstance, triggerData, config);

        return triggerInstance;
    }

    // 工作流启动 - 利用 Effektif 完整生命周期
    public WorkflowInstance startWorkflow(TriggerInstance triggerInstance) {
        // 调用 Effektif 引擎启动，包含完整的生命周期管理
        return workflowEngine.start(triggerInstance);
    }

    // 工作流实例查询 - 基于租户隔离
    public List<WorkflowInstance> findWorkflowInstances(String tenantId, WorkflowInstanceQuery query) {
        // 添加租户过滤条件
        query.property("tenantId", tenantId);
        return workflowEngine.findWorkflowInstances(query);
    }

    // 数据提取方法 - 利用 DataContainer
    public Map<String, Object> extractBusinessData(String tenantId, WorkflowInstance workflowInstance) {
        return tenantDataProcessor.extractTenantBusinessData(tenantId, workflowInstance);
    }

    public Map<String, Object> extractRuntimeData(String tenantId, WorkflowInstance workflowInstance) {
        return tenantDataProcessor.extractTenantRuntimeData(tenantId, workflowInstance);
    }
}
```

## 10. Effektif 集成配置和最佳实践

### 10.1 Effektif 配置策略

#### 10.1.1 开发环境配置（Memory 存储）

```yaml
# application-dev.yaml
ipaas:
  flow:
    engine:
      effektif:
        configuration-type: memory  # 使用 MemoryConfiguration
        storage:
          type: memory
          cleanup-on-shutdown: true
        execution:
          mode: synchronous  # 同步执行，便于调试
          timeout: 30000
```

#### 10.1.2 测试环境配置（TestConfiguration）

```yaml
# application-test.yaml
ipaas:
  flow:
    engine:
      effektif:
        configuration-type: test  # 使用 TestConfiguration
        storage:
          type: memory
          auto-cleanup: true
        execution:
          mode: synchronous  # 测试用同步模式
          max-instances: 100
```

#### 10.1.3 生产环境配置（MongoDB 存储）

```yaml
# application-prod.yaml
ipaas:
  flow:
    engine:
      effektif:
        configuration-type: mongo  # 使用 MongoConfiguration
        storage:
          type: mongodb
          uri: mongodb://localhost:27017/ipaas_effektif
          database-per-tenant: true
        execution:
          mode: asynchronous  # 异步执行
          thread-pool-size: 50
          timeout: 300000
```

### 10.2 DataContainer 数据处理最佳实践

#### 10.2.1 数据分类策略

```java
@Component
public class DataClassificationStrategy {

    // 业务数据判断规则（存储到 data 字段）
    public boolean isBusinessData(String fieldName, Object value) {
        return businessDataPatterns.stream()
            .anyMatch(pattern -> pattern.matcher(fieldName).matches()) ||
            isAuditableData(fieldName, value) ||
            isCrossNodeData(fieldName, value);
    }

    // 运行时数据判断规则（存储到 transientData 字段）
    public boolean isRuntimeData(String fieldName, Object value) {
        return runtimeDataPatterns.stream()
            .anyMatch(pattern -> pattern.matcher(fieldName).matches()) ||
            isTemporaryData(fieldName, value) ||
            isSensitiveData(fieldName, value);
    }

    // 敏感数据判断规则（仅存储到 transientData 字段）
    public boolean isSensitiveData(String fieldName, Object value) {
        return sensitiveFieldPatterns.stream()
            .anyMatch(pattern -> pattern.matcher(fieldName).matches());
    }
}
```

### 10.3 多租户数据隔离实现

```java
@Component
public class TenantIsolationManager {

    public void applyTenantIsolation(String tenantId, DataContainer container) {
        // 在 data 字段中添加租户标识
        container.data("tenantId", tenantId);
        container.data("tenantContext", createTenantContext(tenantId));

        // 在 transientData 中添加租户运行时信息
        container.transientData("tenantRuntimeId", generateTenantRuntimeId(tenantId));
        container.transientData("tenantSecurityContext", createSecurityContext(tenantId));
    }

    public boolean validateTenantAccess(String tenantId, DataContainer container) {
        String containerTenantId = (String) container.getVariableValue("tenantId");
        return Objects.equals(tenantId, containerTenantId);
    }
}
```

## 11. 测试策略

### 11.1 基于 Effektif 的单元测试

- **服务层测试**：测试各 Service 接口的独立功能，使用 TestConfiguration
- **模型转换测试**：测试 iPaaS 模型与 Effektif API 的转换
- **多租户隔离测试**：验证 tenantId 过滤和数据隔离，基于 DataContainer
- **触发器服务测试**：测试 BaseTriggerService 的数据接收和流程启动
- **DataContainer 测试**：验证 data/transientData 的正确使用

### 11.2 基于 Effektif 的集成测试

- **端到端流程测试**：从 web 层到 Effektif 引擎的完整流程，使用 MemoryConfiguration
- **多租户集成测试**：验证不同租户间的数据隔离，基于 DataContainer 机制
- **触发器集成测试**：测试 base 模块触发器与 flow-engine 的集成
- **Effektif 引擎集成测试**：验证与 Effektif API 的正确集成
- **生命周期集成测试**：测试 Effektif 工作流完整生命周期
- **存储策略测试**：测试 Memory 和 MongoDB 存储的切换

### 11.3 性能测试

- **并发执行测试**：测试多租户并发流程执行能力，利用 Effektif 锁定机制
- **大数据量测试**：测试大量流程定义和执行历史的性能
- **触发器性能测试**：测试高频触发场景下的性能表现
- **DataContainer 性能测试**：测试 data/transientData 的内存使用和性能
- **Effektif 存储性能测试**：对比 Memory 和 MongoDB 存储的性能差异

## 12. 部署与配置

### 12.1 基于 Effektif 的多租户配置

```yaml
# application.yaml
ipaas:
  flow:
    engine:
      # 多租户配置
      tenant:
        isolation-level: DATABASE  # DATABASE, SCHEMA, TABLE
        default-tenant: default
        tenant-header: X-Tenant-ID

      # Effektif引擎配置
      effektif:
        # 配置类型选择
        configuration-type: mongo  # memory, test, mongo, mongo-memory

        # MongoDB 配置（生产环境）
        mongodb:
          uri: mongodb://localhost:27017/ipaas_effektif
          database-per-tenant: true  # 每个租户独立数据库

        # Memory 配置（开发/测试环境）
        memory:
          cleanup-on-shutdown: true
          max-instances: 1000

        # 执行器配置
        executor:
          thread-pool-size: 20
          tenant-aware: true
          async-enabled: true

      # DataContainer 数据处理配置
      data-processing:
        business-data:
          auto-persist: true        # 自动持久化业务数据
          type-inference: true      # 自动类型推断
        runtime-data:
          auto-cleanup: true        # 自动清理运行时数据
          max-size: 1MB            # 运行时数据最大大小
        sensitive-data:
          encryption-enabled: true  # 敏感数据加密
          auto-cleanup: true       # 自动清理敏感数据

      # 基础触发器配置
      trigger:
        base:
          timeout: 30000           # 基础触发器超时时间
          max-concurrent: 100      # 最大并发触发数
          validation-enabled: true # 启用触发数据验证
          data-classification: true # 启用数据分类

      # 执行配置
      execution:
        timeout: 300000            # 默认执行超时时间(毫秒)
        max-retries: 3             # 默认最大重试次数
        retry-delay: 60000         # 重试间隔(毫秒)
        tenant-isolation: true     # 启用租户执行隔离
        lock-timeout: 60000        # Effektif 实例锁定超时

      # 存储配置
      storage:
        cleanup-days: 30           # 执行记录保留天数
        tenant-cleanup: true       # 按租户清理数据
        effektif-cleanup: true     # 启用 Effektif 存储清理
```

### 12.2 Effektif 集成的服务依赖配置

```yaml
# 服务依赖注入配置
spring:
  profiles:
    active: dev

  # MongoDB 多租户配置（Effektif 集成）
  data:
    mongodb:
      uri: mongodb://localhost:27017/
      database: ipaas_effektif_${tenant:default}

# Effektif 组件配置
effektif:
  workflow:
    # 工作流引擎配置
    engine:
      id: ${spring.application.name}-${random.uuid}

    # 存储配置
    storage:
      workflows-collection: effektif_workflows
      instances-collection: effektif_instances
      jobs-collection: effektif_jobs
      tasks-collection: effektif_tasks

    # 缓存配置
    cache:
      workflow-cache-size: 1000
      instance-cache-size: 5000

# 组件扫描配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,effektif
```

## 12. 开发实施计划

### 12.1 第一阶段：核心服务层

**目标**：建立 ipaas-flow-engine 核心服务层

- 实现 FlowService 接口和基础实现
- 实现 TenantFlowService 多租户包装层
- 实现 BaseTriggerService 基础触发器服务
- 集成 Effektif 引擎，实现 EffektifAdapter
- 建立多租户数据模型和 Repository

### 12.2 第二阶段：API 层集成

**目标**：web 和 base 模块集成

- ipaas-web 模块实现 Controller 层，调用 flow-engine 服务
- ipaas-core 模块实现具体触发器（Polling、Webhook、Schedule）
- 建立标准化的 API 接口规范
- 实现触发器到 BaseTriggerService 的调用链路

### 12.3 第三阶段：功能完善和优化

**目标**：完善功能和性能优化

- 实现 ActionService 和动作执行链
- 完善流程执行历史和监控
- 实现多租户数据隔离和安全
- 性能优化和并发处理
- 完善错误处理和重试机制

### 12.4 第四阶段：扩展和生态

**目标**：扩展能力和生态建设

- 扩展更多触发器类型和动作类型
- 实现流程版本管理和回滚
- 添加流程执行监控和告警
- 建立连接器生态和插件机制

## 13. 总结

### 13.1 基于 Effektif 的架构特点

1. **深度集成 Effektif**：充分利用 Effektif 工作流引擎的完整生命周期管理
2. **分层清晰**：ipaas-flow-engine 专注于服务层，基于 Effektif API 构建
3. **多租户支持**：基于 Effektif DataContainer 机制，实现数据隔离和安全处理
4. **触发器抽象**：BaseTriggerService 基于 Effektif TriggerInstance，具体实现在 base 模块
5. **数据处理优化**：利用 data/transientData 机制，优化存储和性能

### 13.2 关键设计原则

- **Effektif 优先**：充分利用 Effektif 的成熟功能，避免重复造轮子
- **单一职责**：每个模块职责明确，flow-engine 专注于 Effektif 集成和多租户包装
- **依赖倒置**：base 模块依赖 flow-engine 的抽象接口
- **开放封闭**：基于 Effektif 扩展机制，易于扩展新的触发器和动作类型
- **多租户优先**：所有设计都基于 DataContainer 考虑多租户场景
- **数据分离**：严格区分持久化数据和临时数据，优化性能和安全

### 13.3 技术优势

- **成熟的工作流引擎**：基于 Effektif 的完整工作流生命周期管理
- **多租户 SaaS 架构**：基于 DataContainer 的数据隔离和安全处理
- **灵活的存储策略**：支持 Memory（开发/测试）和 MongoDB（生产）存储
- **高性能数据处理**：利用 data/transientData 机制优化内存使用
- **完善的并发控制**：基于 Effektif 的锁定机制和状态管理
- **标准化的 API 接口**：与 Effektif API 保持一致的接口规范
- **易于测试和调试**：支持同步/异步执行模式切换

### 13.4 Effektif 集成价值

1. **减少开发工作量**：利用 Effektif 现有的工作流引擎功能
2. **提高系统稳定性**：基于成熟的 Effektif 实现，减少 Bug 风险
3. **优化性能表现**：利用 Effektif 的内存管理和并发控制
4. **简化运维管理**：统一的 Effektif 配置和监控体系
5. **增强扩展能力**：基于 Effektif 的插件机制，易于功能扩展
