<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke.erpdss</groupId>
        <artifactId>fs-erp-ipaas</artifactId>
        <version>2.0-SNAPSHOT</version>
    </parent>

    <artifactId>ipaas-app-all</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fxiaoke.erpdss</groupId>
            <artifactId>ipaas-app-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.erpdss</groupId>
            <artifactId>ipaas-app-web</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.erpdss</groupId>
            <artifactId>ipaas-flow-engine</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 集成测试，每个模块按需引入-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>