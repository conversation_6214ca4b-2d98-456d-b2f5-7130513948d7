package com.fxiaoke.erpdss.ipaas.base.trigger;

import com.fxiaoke.erpdss.ipaas.flow.TriggerResult;
import com.fxiaoke.erpdss.ipaas.flow.ValidationResult;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 轮询触发器实现
 * 定期轮询外部API或数据源，检测数据变化
 * 
 * <AUTHOR> (^_−)☆
 */
@Component
public class PollingTrigger extends BaseTrigger {

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(10);
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    private final Map<String, Object> lastPollingResults = new ConcurrentHashMap<>();
    private final WebClient webClient = WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024)) // 1MB
            .build();

    @Override
    public String getTriggerType() {
        return "polling";
    }

    @Override
    public String getTriggerName() {
        return "轮询触发器";
    }

    @Override
    public String getTriggerDescription() {
        return "定期轮询外部API或数据源，检测数据变化时触发流程";
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        ValidationResult result = validateBaseConfig(config);
        
        try {
            // 验证必需的配置
            getRequiredConfigValue(config, "url");
            
            // 验证轮询间隔
            Object intervalObj = getConfigValue(config, "interval", 60);
            int interval = parseInterval(intervalObj);
            if (interval < 5) {
                result.addError("Polling interval must be at least 5 seconds");
            }
            
            // 验证URL格式
            String url = (String) config.get("url");
            if (!isValidUrl(url)) {
                result.addError("Invalid URL format: " + url);
            }
            
            // 验证HTTP方法
            String method = (String) getConfigValue(config, "method", "GET");
            if (!isValidHttpMethod(method)) {
                result.addError("Invalid HTTP method: " + method);
            }
            
        } catch (NumberFormatException e) {
            result.addError("Invalid interval format: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            result.addError(e.getMessage());
        }
        
        return result;
    }

    @Override
    public void start(String triggerId, Map<String, Object> config) {
        try {
            logger.info("Starting polling trigger: {}", triggerId);
            
            // 验证配置
            ValidationResult validation = validateConfig(config);
            if (!validation.isValid()) {
                throw new IllegalArgumentException("Invalid configuration: " + validation.getErrors());
            }
            
            // 停止已存在的任务
            stop(triggerId);
            
            // 获取配置参数
            String url = (String) getRequiredConfigValue(config, "url");
            int interval = parseInterval(getConfigValue(config, "interval", 60));
            String method = (String) getConfigValue(config, "method", "GET");
            boolean detectChanges = (Boolean) getConfigValue(config, "detectChanges", true);
            
            // 创建轮询任务
            ScheduledFuture<?> task = scheduler.scheduleWithFixedDelay(
                () -> performPolling(triggerId, url, method, detectChanges, config),
                0, // 立即开始
                interval,
                TimeUnit.SECONDS
            );
            
            scheduledTasks.put(triggerId, task);
            
            logger.info("Polling trigger started successfully: {} (interval: {}s, url: {})", 
                triggerId, interval, url);
            
        } catch (Exception e) {
            handleError(triggerId, "start", e);
            throw new RuntimeException("Failed to start polling trigger: " + triggerId, e);
        }
    }

    @Override
    public void stop(String triggerId) {
        try {
            logger.info("Stopping polling trigger: {}", triggerId);
            
            ScheduledFuture<?> task = scheduledTasks.remove(triggerId);
            if (task != null) {
                task.cancel(true);
            }
            
            // 清理轮询结果缓存
            lastPollingResults.remove(triggerId);
            
            logger.info("Polling trigger stopped successfully: {}", triggerId);
            
        } catch (Exception e) {
            handleError(triggerId, "stop", e);
        }
    }

    /**
     * 执行轮询操作
     */
    private void performPolling(String triggerId, String url, String method, 
                               boolean detectChanges, Map<String, Object> config) {
        try {
            logger.debug("Performing polling for trigger: {} (url: {})", triggerId, url);
            
            // 构建请求
            WebClient.RequestHeadersSpec<?> request = buildRequest(url, method, config);
            
            // 执行请求
            String response = request
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(30))
                .block();
            
            // 检测变化
            if (detectChanges) {
                Object lastResult = lastPollingResults.get(triggerId);
                if (lastResult != null && lastResult.equals(response)) {
                    logger.debug("No changes detected for trigger: {}", triggerId);
                    return;
                }
                lastPollingResults.put(triggerId, response);
            }
            
            // 构建触发数据
            Map<String, Object> triggerData = new ConcurrentHashMap<>();
            triggerData.put("url", url);
            triggerData.put("method", method);
            triggerData.put("response", response);
            triggerData.put("pollingTime", System.currentTimeMillis());
            
            // 触发流程
            TriggerResult result = triggerFlow(triggerId, triggerData);
            
            if (result.isSuccess()) {
                logger.debug("Polling trigger executed successfully: {}", triggerId);
            } else {
                logger.warn("Polling trigger execution failed: {} - {}", triggerId, result.getErrorMessage());
            }
            
        } catch (Exception e) {
            logger.error("Error during polling for trigger: " + triggerId, e);
        }
    }

    /**
     * 构建HTTP请求
     */
    private WebClient.RequestHeadersSpec<?> buildRequest(String url, String method, Map<String, Object> config) {
        WebClient.RequestBodyUriSpec request = webClient.method(
            org.springframework.http.HttpMethod.valueOf(method.toUpperCase())
        );
        
        WebClient.RequestHeadersSpec<?> headersSpec = request.uri(url);
        
        // 添加请求头
        @SuppressWarnings("unchecked")
        Map<String, String> headers = (Map<String, String>) getConfigValue(config, "headers", Map.of());
        headers.forEach(headersSpec::header);
        
        // 添加认证
        String authType = (String) getConfigValue(config, "authType", "none");
        if ("bearer".equalsIgnoreCase(authType)) {
            String token = (String) getConfigValue(config, "token", "");
            if (!token.isEmpty()) {
                headersSpec.header("Authorization", "Bearer " + token);
            }
        } else if ("basic".equalsIgnoreCase(authType)) {
            String username = (String) getConfigValue(config, "username", "");
            String password = (String) getConfigValue(config, "password", "");
            if (!username.isEmpty()) {
                headersSpec.headers(httpHeaders -> 
                    httpHeaders.setBasicAuth(username, password));
            }
        }
        
        return headersSpec;
    }

    /**
     * 解析间隔时间
     */
    private int parseInterval(Object intervalObj) {
        if (intervalObj instanceof Number) {
            return ((Number) intervalObj).intValue();
        } else if (intervalObj instanceof String) {
            return Integer.parseInt((String) intervalObj);
        } else {
            throw new NumberFormatException("Invalid interval type: " + intervalObj.getClass());
        }
    }

    /**
     * 验证URL格式
     */
    private boolean isValidUrl(String url) {
        return url != null && 
               (url.startsWith("http://") || url.startsWith("https://")) &&
               url.length() > 10;
    }

    /**
     * 验证HTTP方法
     */
    private boolean isValidHttpMethod(String method) {
        return method != null && 
               (method.equalsIgnoreCase("GET") || 
                method.equalsIgnoreCase("POST") || 
                method.equalsIgnoreCase("PUT") || 
                method.equalsIgnoreCase("DELETE"));
    }

    /**
     * 获取活跃的轮询任务
     */
    public Map<String, ScheduledFuture<?>> getActiveTasks() {
        return new ConcurrentHashMap<>(scheduledTasks);
    }

    /**
     * 清理资源
     */
    public void shutdown() {
        logger.info("Shutting down polling trigger scheduler");
        scheduledTasks.values().forEach(task -> task.cancel(true));
        scheduledTasks.clear();
        scheduler.shutdown();
    }
}
