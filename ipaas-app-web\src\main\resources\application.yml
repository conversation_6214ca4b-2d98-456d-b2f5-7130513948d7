# iPaaS Web Application Configuration

spring:
  profiles:
    active: dev
  
  application:
    name: ipaas-web
  
  # MongoDB配置
  data:
    mongodb:
      uri: mongodb://localhost:27017/ipaas_web
      auto-index-creation: true

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# iPaaS Flow Engine配置
ipaas:
  flow:
    engine:
      # Effektif引擎配置
      effektif:
        configuration-type: memory
        
      # 执行配置
      execution:
        timeout: 300000
        max-retries: 3
        retry-delay: 60000
        
      # 存储配置
      storage:
        cleanup-days: 30

# OpenAPI文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operations-sorter: alpha
    tags-sorter: alpha
  show-actuator: true

# 日志配置
logging:
  level:
    com.fxiaoke.erpdss.ipaas: INFO
    com.effektif: WARN
    org.springframework.web: WARN
    org.mongodb: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ipaas-web.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    com.fxiaoke.erpdss.ipaas: DEBUG

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
      
spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/ipaas_web_test

server:
  port: 8081

logging:
  level:
    com.fxiaoke.erpdss.ipaas: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

spring:
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017/ipaas_web_prod}

server:
  port: ${SERVER_PORT:8080}

ipaas:
  flow:
    engine:
      effektif:
        configuration-type: mongo
        mongodb:
          uri: ${EFFEKTIF_MONGODB_URI:mongodb://localhost:27017/ipaas_effektif_prod}

logging:
  level:
    com.fxiaoke.erpdss.ipaas: INFO
    root: WARN
