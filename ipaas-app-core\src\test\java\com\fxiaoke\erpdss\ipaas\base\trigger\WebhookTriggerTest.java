package com.fxiaoke.erpdss.ipaas.base.trigger;

import com.fxiaoke.erpdss.ipaas.flow.TriggerResult;
import com.fxiaoke.erpdss.ipaas.flow.TriggerService;
import com.fxiaoke.erpdss.ipaas.flow.ValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * WebhookTrigger单元测试
 * 
 * <AUTHOR> (^_−)☆
 */
@ExtendWith(MockitoExtension.class)
class WebhookTriggerTest {

    @Mock
    private TriggerService triggerService;

    @InjectMocks
    private WebhookTrigger webhookTrigger;

    private Map<String, Object> validConfig;
    private String testTriggerId;

    @BeforeEach
    void setUp() {
        testTriggerId = "test-webhook-trigger";
        
        validConfig = new HashMap<>();
        validConfig.put("path", "/test-webhook");
        validConfig.put("method", "POST");
        validConfig.put("enabled", true);
    }

    @Test
    void testGetTriggerType() {
        assertEquals("webhook", webhookTrigger.getTriggerType());
    }

    @Test
    void testGetTriggerName() {
        assertEquals("Webhook触发器", webhookTrigger.getTriggerName());
    }

    @Test
    void testGetTriggerDescription() {
        assertNotNull(webhookTrigger.getTriggerDescription());
        assertTrue(webhookTrigger.getTriggerDescription().contains("HTTP"));
    }

    @Test
    void testValidateConfigValid() {
        ValidationResult result = webhookTrigger.validateConfig(validConfig);
        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
    }

    @Test
    void testValidateConfigMissingPath() {
        validConfig.remove("path");
        ValidationResult result = webhookTrigger.validateConfig(validConfig);
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream().anyMatch(error -> error.contains("path")));
    }

    @Test
    void testValidateConfigInvalidMethod() {
        validConfig.put("method", "INVALID");
        ValidationResult result = webhookTrigger.validateConfig(validConfig);
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream().anyMatch(error -> error.contains("Invalid HTTP method")));
    }

    @Test
    void testValidateConfigInvalidPath() {
        validConfig.put("path", "invalid-path");
        ValidationResult result = webhookTrigger.validateConfig(validConfig);
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream().anyMatch(error -> error.contains("Invalid webhook path")));
    }

    @Test
    void testStartTrigger() {
        assertDoesNotThrow(() -> {
            webhookTrigger.start(testTriggerId, validConfig);
        });
        
        // 验证触发器已启动
        assertTrue(webhookTrigger.getActiveWebhooks().containsKey(testTriggerId));
    }

    @Test
    void testStartTriggerInvalidConfig() {
        validConfig.remove("path");
        
        assertThrows(RuntimeException.class, () -> {
            webhookTrigger.start(testTriggerId, validConfig);
        });
    }

    @Test
    void testStopTrigger() {
        // 先启动触发器
        webhookTrigger.start(testTriggerId, validConfig);
        assertTrue(webhookTrigger.getActiveWebhooks().containsKey(testTriggerId));
        
        // 停止触发器
        webhookTrigger.stop(testTriggerId);
        assertFalse(webhookTrigger.getActiveWebhooks().containsKey(testTriggerId));
    }

    @Test
    void testHandlePostWebhook() {
        // 启动触发器
        webhookTrigger.start(testTriggerId, validConfig);
        
        // 模拟TriggerService返回成功结果
        when(triggerService.triggerFlow(eq(testTriggerId), any())).thenReturn(
            TriggerResult.success(testTriggerId, "execution-id-123")
        );
        
        // 准备请求数据
        Map<String, Object> payload = new HashMap<>();
        payload.put("data", "test-data");
        
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("param1", "value1");
        
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        
        // 调用webhook
        TriggerResult result = webhookTrigger.handlePostWebhook(testTriggerId, payload, queryParams, headers);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(testTriggerId, result.getTriggerId());
    }

    @Test
    void testHandleWebhookTriggerNotFound() {
        Map<String, Object> payload = new HashMap<>();
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        TriggerResult result = webhookTrigger.handlePostWebhook("non-existent-trigger", payload, queryParams, headers);
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("not found"));
    }

    @Test
    void testHandleWebhookWrongMethod() {
        // 启动只允许POST的触发器
        webhookTrigger.start(testTriggerId, validConfig);
        
        Map<String, String> queryParams = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        
        // 尝试GET请求
        TriggerResult result = webhookTrigger.handleGetWebhook(testTriggerId, queryParams, headers);
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("not allowed"));
    }

    @Test
    void testGetActiveWebhooks() {
        assertTrue(webhookTrigger.getActiveWebhooks().isEmpty());
        
        webhookTrigger.start(testTriggerId, validConfig);
        
        Map<String, Map<String, Object>> activeWebhooks = webhookTrigger.getActiveWebhooks();
        assertEquals(1, activeWebhooks.size());
        assertTrue(activeWebhooks.containsKey(testTriggerId));
    }
}
