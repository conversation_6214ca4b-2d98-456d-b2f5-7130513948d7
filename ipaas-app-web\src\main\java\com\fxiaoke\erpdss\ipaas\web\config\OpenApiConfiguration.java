package com.fxiaoke.erpdss.ipaas.web.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI文档配置
 * 
 * <AUTHOR> (^_−)☆
 */
@Configuration
public class OpenApiConfiguration {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("iPaaS Flow Engine API")
                .description("Integration Platform as a Service - Flow Engine REST API")
                .version("2.0-SNAPSHOT")
                .contact(new Contact()
                    .name("iPaaS Team")
                    .email("<EMAIL>"))
                .license(new License()
                    .name("Apache 2.0")
                    .url("https://www.apache.org/licenses/LICENSE-2.0")))
            .servers(List.of(
                new Server()
                    .url("http://localhost:8080")
                    .description("Development Server"),
                new Server()
                    .url("https://api.ipaas.fxiaoke.com")
                    .description("Production Server")
            ));
    }
}
