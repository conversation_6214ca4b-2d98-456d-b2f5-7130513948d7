version: '3.8'

services:
  mongodb-test:
    image: mongo:4.0
    container_name: ipaas-db-proxy-mongodb-test
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: ipaas_db_proxy_test
    volumes:
      - mongodb_test_data:/data/db
      - ./src/test/resources/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - ipaas-test-network
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

volumes:
  mongodb_test_data:
    driver: local

networks:
  ipaas-test-network:
    driver: bridge
