package com.fxiaoke.erpdss.ipaas.flow.service;

import com.effektif.workflow.api.WorkflowEngine;
import com.effektif.workflow.api.model.Deployment;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.query.WorkflowQuery;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSBizException;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSSystemException;
import com.fxiaoke.erpdss.ipaas.common.module.Result;
import com.fxiaoke.erpdss.ipaas.common.module.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * 流程管理服务实现类
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Service
public class FlowManagementServiceImpl implements FlowManagementService {

    private WorkflowEngine workflowEngine;

    @Autowired
    public void setWorkflowEngine(WorkflowEngine workflowEngine) {
        this.workflowEngine = workflowEngine;
    }

    /**
     * 发布流程
     *
     * @param workflow 工作流定义
     * @return 发布结果
     */
    @Override
    public Result<ExecutableWorkflow> deployFlow(ExecutableWorkflow workflow) {
        log.info("Starting to deploy workflow, workflow name: {}", workflow != null ? workflow.getName() : "null");

        // 参数验证
        if (workflow == null) {
            log.warn("Failed to deploy workflow: workflow definition is null");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Workflow definition cannot be null");
        }

        if (!StringUtils.hasText(workflow.getName())) {
            log.warn("Failed to deploy workflow: workflow name is empty");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Workflow name cannot be empty");
        }

        try {
            // 调用工作流引擎部署流程
            Deployment deployment = workflowEngine.deployWorkflow(workflow);

            // 检查部署结果
            if (deployment.hasErrors()) {
                log.error("Failed to deploy workflow, errors found: {}", deployment.getIssues());
                return Result.error(ResultCode.BIZ_ERROR, "Failed to deploy workflow: " + deployment.getIssues().toString());
            }

            if (deployment.hasIssues() && !deployment.hasErrors()) {
                log.warn("Workflow deployed successfully but with warnings: {}", deployment.getIssues());
            }

            log.info("Workflow deployed successfully, workflow ID: {}, workflow name: {}",
                    deployment.getWorkflowId(), workflow.getName());

            return Result.success(workflow, "Workflow deployed successfully");

        } catch (Exception e) {
            log.error("Exception occurred while deploying workflow, workflow name: {}", workflow.getName(), e);
            if (e instanceof IPaaSBizException || e instanceof IPaaSSystemException) {
                throw e;
            }
            throw new IPaaSSystemException("System exception occurred while deploying workflow", e);
        }
    }

    /**
     * 更新流程
     *
     * @param workflow 工作流定义
     * @return 更新结果
     */
    @Override
    public Result<ExecutableWorkflow> updateFlow(ExecutableWorkflow workflow) {
        log.info("Starting to update workflow, workflow ID: {}, workflow name: {}",
                workflow != null ? workflow.getId() : "null",
                workflow != null ? workflow.getName() : "null");

        // 参数验证
        if (workflow == null) {
            log.warn("Failed to update workflow: workflow definition is null");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Workflow definition cannot be null");
        }

        if (workflow.getId() == null) {
            log.warn("Failed to update workflow: workflow ID is null");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Workflow ID cannot be null");
        }

        if (!StringUtils.hasText(workflow.getName())) {
            log.warn("Failed to update workflow: workflow name is empty");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Workflow name cannot be empty");
        }

        try {
            // 调用工作流引擎更新流程
            Deployment deployment = workflowEngine.updateWorkflow(workflow);

            // 检查更新结果
            if (deployment.hasErrors()) {
                log.error("Failed to update workflow, errors found: {}", deployment.getIssues());
                return Result.error(ResultCode.BIZ_ERROR, "Failed to update workflow: " + deployment.getIssues().toString());
            }

            if (deployment.hasIssues() && !deployment.hasErrors()) {
                log.warn("Workflow updated successfully but with warnings: {}", deployment.getIssues());
            }

            log.info("Workflow updated successfully, workflow ID: {}, workflow name: {}",
                    deployment.getWorkflowId(), workflow.getName());

            return Result.success(workflow, "Workflow updated successfully");

        } catch (Exception e) {
            log.error("Exception occurred while updating workflow, workflow ID: {}, workflow name: {}",
                    workflow.getId(), workflow.getName(), e);
            if (e instanceof IPaaSBizException || e instanceof IPaaSSystemException) {
                throw e;
            }
            throw new IPaaSSystemException("System exception occurred while updating workflow", e);
        }
    }

    /**
     * 根据条件查找流程列表
     *
     * @param tenantId      租户ID
     * @param workflowQuery 查询条件
     * @return 流程列表
     */
    @Override
    public Result<List<ExecutableWorkflow>> findWorkflows(String tenantId, WorkflowQuery workflowQuery) {
        log.debug("Starting to find workflows, tenant ID: {}, query: {}", tenantId, workflowQuery);

        // 参数验证
        if (!StringUtils.hasText(tenantId)) {
            log.warn("Failed to find workflows: tenant ID is empty");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Tenant ID cannot be empty");
        }

        try {
            // 调用工作流引擎查找流程
            List<ExecutableWorkflow> workflows = workflowEngine.findWorkflows(tenantId, workflowQuery);

            if (workflows == null) {
                log.debug("No matching workflows found, tenant ID: {}", tenantId);
                return Result.success(Collections.emptyList(), "No workflows found");
            }

            log.debug("Found {} workflows, tenant ID: {}", workflows.size(), tenantId);
            return Result.success(workflows, "Workflows found successfully");

        } catch (Exception e) {
            log.error("Exception occurred while finding workflows, tenant ID: {}", tenantId, e);
            if (e instanceof IPaaSBizException || e instanceof IPaaSSystemException) {
                throw e;
            }
            throw new IPaaSSystemException("System exception occurred while finding workflows", e);
        }
    }

    /**
     * 根据ID获取特定流程
     *
     * @param tenantId 租户ID
     * @param workflowSource 工作流源标识
     * @return 流程详情
     */
    @Override
    public Result<ExecutableWorkflow> getFlow(String tenantId, String workflowSource) {
        log.debug("Starting to get workflow details, tenant ID: {}, workflow source: {}", tenantId, workflowSource);

        // 参数验证
        if (!StringUtils.hasText(tenantId)) {
            log.warn("Failed to get workflow: tenant ID is empty");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Tenant ID cannot be empty");
        }

        if (!StringUtils.hasText(workflowSource)) {
            log.warn("Failed to get workflow: workflow source is empty");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Workflow source cannot be empty");
        }

        try {
            // 构建查询条件
            WorkflowQuery query = new WorkflowQuery();
            query.workflowSource(workflowSource);

            // 调用工作流引擎查找流程
            List<ExecutableWorkflow> workflows = workflowEngine.findWorkflows(tenantId, query);

            if (workflows == null || workflows.isEmpty()) {
                log.warn("Workflow not found, tenant ID: {}, workflow source: {}", tenantId, workflowSource);
                return Result.error(ResultCode.BIZ_ERROR, "Workflow not found");
            }

            if (workflows.size() > 1) {
                log.warn("Multiple workflows found, tenant ID: {}, workflow source: {}, count: {}",
                        tenantId, workflowSource, workflows.size());
            }

            ExecutableWorkflow workflow = workflows.get(0);
            log.debug("Successfully retrieved workflow details, tenant ID: {}, workflow source: {}, workflow name: {}",
                    tenantId, workflowSource, workflow.getName());

            return Result.success(workflow, "Workflow retrieved successfully");

        } catch (Exception e) {
            log.error("Exception occurred while getting workflow, tenant ID: {}, workflow source: {}", tenantId, workflowSource, e);
            if (e instanceof IPaaSBizException || e instanceof IPaaSSystemException) {
                throw e;
            }
            throw new IPaaSSystemException("System exception occurred while getting workflow", e);
        }
    }

    /**
     * 启动工作流实例
     *
     * @param triggerInstance 触发实例
     * @return 工作流实例
     */
    @Override
    public Result<WorkflowInstance> start(TriggerInstance triggerInstance) {
        log.info("Starting workflow instance, tenant ID: {}, workflow ID: {}, source workflow ID: {}",
                triggerInstance != null ? triggerInstance.getTenantId() : "null",
                triggerInstance != null ? triggerInstance.getWorkflowId() : "null",
                triggerInstance != null ? triggerInstance.getSourceWorkflowId() : "null");

        // 参数验证
        if (triggerInstance == null) {
            log.warn("Failed to start workflow: trigger instance is null");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Trigger instance cannot be null");
        }

        if (!StringUtils.hasText(triggerInstance.getTenantId())) {
            log.warn("Failed to start workflow: tenant ID is empty");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Tenant ID cannot be empty");
        }

        if (triggerInstance.getWorkflowId() == null && !StringUtils.hasText(triggerInstance.getSourceWorkflowId())) {
            log.warn("Failed to start workflow: both workflow ID and source workflow ID are empty");
            return Result.error(ResultCode.PARAM_ILLEGAL, "Either workflow ID or source workflow ID must be provided");
        }

        try {
            // 调用工作流引擎启动实例
            WorkflowInstance workflowInstance = workflowEngine.start(triggerInstance);

            if (workflowInstance == null) {
                log.error("Failed to start workflow: workflow instance is null");
                return Result.error(ResultCode.BIZ_ERROR, "Failed to start workflow instance");
            }

            log.info("Workflow instance started successfully, instance ID: {}, workflow ID: {}, tenant ID: {}",
                    workflowInstance.getId(), workflowInstance.getWorkflowId(), workflowInstance.getTenantId());

            return Result.success(workflowInstance, "Workflow instance started successfully");

        } catch (Exception e) {
            log.error("Exception occurred while starting workflow, tenant ID: {}, workflow ID: {}",
                    triggerInstance.getTenantId(), triggerInstance.getWorkflowId(), e);
            if (e instanceof IPaaSBizException || e instanceof IPaaSSystemException) {
                throw e;
            }
            throw new IPaaSSystemException("System exception occurred while starting workflow", e);
        }
    }
}
