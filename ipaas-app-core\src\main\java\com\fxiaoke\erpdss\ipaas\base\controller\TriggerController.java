package com.fxiaoke.erpdss.ipaas.base.controller;

import com.fxiaoke.erpdss.ipaas.base.trigger.TriggerManager;
import com.fxiaoke.erpdss.ipaas.flow.TriggerTemplate;
import com.fxiaoke.erpdss.ipaas.flow.TriggerType;
import com.fxiaoke.erpdss.ipaas.flow.ValidationResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 触发器管理REST API
 * 
 * <AUTHOR> (^_−)☆
 */
@RestController
@RequestMapping("/api/triggers")
public class TriggerController {

    @Autowired
    private TriggerManager triggerManager;

    /**
     * 获取支持的触发器类型
     */
    @GetMapping("/types")
    public ResponseEntity<List<TriggerType>> getSupportedTriggerTypes() {
        List<TriggerType> types = triggerManager.getSupportedTriggerTypes();
        return ResponseEntity.ok(types);
    }

    /**
     * 获取触发器模板
     */
    @GetMapping("/templates/{triggerType}")
    public ResponseEntity<TriggerTemplate> getTriggerTemplate(@PathVariable String triggerType) {
        try {
            TriggerTemplate template = triggerManager.getTriggerTemplate(triggerType);
            return ResponseEntity.ok(template);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 验证触发器配置
     */
    @PostMapping("/validate")
    public ResponseEntity<ValidationResult> validateTriggerConfig(@RequestBody ValidateConfigRequest request) {
        ValidationResult result = triggerManager.validateTriggerConfig(request.getTriggerType(), request.getConfig());
        return ResponseEntity.ok(result);
    }

    /**
     * 启动触发器
     */
    @PostMapping("/{triggerId}/start")
    public ResponseEntity<String> startTrigger(@PathVariable String triggerId, @RequestBody StartTriggerRequest request) {
        try {
            triggerManager.startTrigger(triggerId, request.getTriggerType(), request.getConfig());
            return ResponseEntity.ok("Trigger started successfully");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to start trigger: " + e.getMessage());
        }
    }

    /**
     * 停止触发器
     */
    @PostMapping("/{triggerId}/stop")
    public ResponseEntity<String> stopTrigger(@PathVariable String triggerId) {
        try {
            triggerManager.stopTrigger(triggerId);
            return ResponseEntity.ok("Trigger stopped successfully");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to stop trigger: " + e.getMessage());
        }
    }

    /**
     * 获取活跃的触发器列表
     */
    @GetMapping("/active")
    public ResponseEntity<List<TriggerManager.TriggerInstance>> getActiveTriggers() {
        List<TriggerManager.TriggerInstance> triggers = triggerManager.getActiveTriggers();
        return ResponseEntity.ok(triggers);
    }

    /**
     * 获取触发器实例详情
     */
    @GetMapping("/{triggerId}")
    public ResponseEntity<TriggerManager.TriggerInstance> getTriggerInstance(@PathVariable String triggerId) {
        TriggerManager.TriggerInstance instance = triggerManager.getTriggerInstance(triggerId);
        if (instance != null) {
            return ResponseEntity.ok(instance);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 检查触发器状态
     */
    @GetMapping("/{triggerId}/status")
    public ResponseEntity<TriggerStatusResponse> getTriggerStatus(@PathVariable String triggerId) {
        boolean active = triggerManager.isTriggerActive(triggerId);
        TriggerStatusResponse response = new TriggerStatusResponse(triggerId, active);
        return ResponseEntity.ok(response);
    }

    /**
     * 验证配置请求
     */
    public static class ValidateConfigRequest {
        private String triggerType;
        private Map<String, Object> config;

        // Getters and Setters
        public String getTriggerType() { return triggerType; }
        public void setTriggerType(String triggerType) { this.triggerType = triggerType; }
        public Map<String, Object> getConfig() { return config; }
        public void setConfig(Map<String, Object> config) { this.config = config; }
    }

    /**
     * 启动触发器请求
     */
    public static class StartTriggerRequest {
        private String triggerType;
        private Map<String, Object> config;

        // Getters and Setters
        public String getTriggerType() { return triggerType; }
        public void setTriggerType(String triggerType) { this.triggerType = triggerType; }
        public Map<String, Object> getConfig() { return config; }
        public void setConfig(Map<String, Object> config) { this.config = config; }
    }

    /**
     * 触发器状态响应
     */
    public static class TriggerStatusResponse {
        private String triggerId;
        private boolean active;

        public TriggerStatusResponse(String triggerId, boolean active) {
            this.triggerId = triggerId;
            this.active = active;
        }

        // Getters and Setters
        public String getTriggerId() { return triggerId; }
        public void setTriggerId(String triggerId) { this.triggerId = triggerId; }
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
    }
}
