package com.fxiaoke.erpdss.ipaas.flow.service;

import com.effektif.workflow.api.WorkflowEngine;
import com.effektif.workflow.api.activities.EndEvent;
import com.effektif.workflow.api.activities.StartEvent;
import com.effektif.workflow.api.model.Deployment;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.model.WorkflowId;
import com.effektif.workflow.api.model.WorkflowInstanceId;
import com.effektif.workflow.api.query.WorkflowQuery;
import com.effektif.workflow.api.types.TextType;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.fxiaoke.erpdss.ipaas.common.module.Result;
import com.fxiaoke.erpdss.ipaas.flow.activity.LogAction;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * FlowManagementServiceImpl 简化集成测试
 * 使用Mock的WorkflowEngine，不依赖完整的Spring上下文
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@DisplayName("流程管理服务简化集成测试")
class FlowManagementServiceImplSimpleIT {

    @Mock
    private WorkflowEngine workflowEngine;

    private FlowManagementServiceImpl flowManagementService;

    private static final String TEST_TENANT_ID = "test-tenant";

    @BeforeEach
    void setUp() {
        flowManagementService = new FlowManagementServiceImpl();
        flowManagementService.setWorkflowEngine(workflowEngine);
    }

    @Test
    @DisplayName("完整流程测试：发布、查找、获取流程")
    void completeFlowTest() {
        // 1. 创建测试流程
        ExecutableWorkflow workflow = createTestWorkflow();
        
        // 2. Mock 发布流程
        Deployment successDeployment = new Deployment();
        successDeployment.setWorkflowId(new WorkflowId("test-workflow-id"));
        when(workflowEngine.deployWorkflow(workflow)).thenReturn(successDeployment);
        
        Result<ExecutableWorkflow> deployResult = flowManagementService.deployFlow(workflow);
        log.info("发布流程结果：{}", deployResult);
        
        assertThat(deployResult.isSuccess()).isTrue();
        assertThat(deployResult.getData()).isEqualTo(workflow);
        assertThat(deployResult.getMessage()).isEqualTo("Workflow deployed successfully");
        
        // 3. Mock 查找流程列表
        List<ExecutableWorkflow> workflows = Arrays.asList(workflow);
        when(workflowEngine.findWorkflows(eq(TEST_TENANT_ID), any(WorkflowQuery.class)))
                .thenReturn(workflows);
        
        WorkflowQuery query = new WorkflowQuery();
        query.tenantId(TEST_TENANT_ID);

        Result<List<ExecutableWorkflow>> findResult = flowManagementService.findWorkflows(TEST_TENANT_ID, query);
        log.info("查找到的流程数量：{}", findResult.getData().size());

        assertThat(findResult.isSuccess()).isTrue();
        assertThat(findResult.getData()).hasSize(1);
        assertThat(findResult.getData().get(0).getName()).isEqualTo("集成测试流程");
        
        // 4. Mock 获取特定流程
        when(workflowEngine.findWorkflows(eq(TEST_TENANT_ID), any(WorkflowQuery.class)))
                .thenReturn(Arrays.asList(workflow));
        
        Result<ExecutableWorkflow> getResult = flowManagementService.getFlow(TEST_TENANT_ID, "integration-test-flow");
        log.info("获取流程结果：{}", getResult);
        
        assertThat(getResult.isSuccess()).isTrue();
        assertThat(getResult.getData()).isEqualTo(workflow);
        assertThat(getResult.getData().getName()).isEqualTo("集成测试流程");
        
        // 5. Mock 更新流程
        ExecutableWorkflow updatedWorkflow = workflow;
        updatedWorkflow.setId(new WorkflowId("test-workflow-id")); // 设置ID以便更新
        updatedWorkflow.setDescription("更新后的描述");

        when(workflowEngine.updateWorkflow(updatedWorkflow)).thenReturn(successDeployment);
        
        Result<ExecutableWorkflow> updateResult = flowManagementService.updateFlow(updatedWorkflow);
        log.info("更新流程结果：{}", updateResult);
        
        assertThat(updateResult.isSuccess()).isTrue();
        assertThat(updateResult.getData().getDescription()).isEqualTo("更新后的描述");
    }

    @Test
    @DisplayName("查找不存在的流程")
    void findNonExistentFlow() {
        // Mock 返回空列表
        when(workflowEngine.findWorkflows(eq(TEST_TENANT_ID), any(WorkflowQuery.class)))
                .thenReturn(Collections.emptyList());
        
        Result<ExecutableWorkflow> result = flowManagementService.getFlow(TEST_TENANT_ID, "non-existent-flow");
        
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getMessage()).contains("Workflow not found");
    }

    @Test
    @DisplayName("空查询条件测试")
    void emptyQueryTest() {
        // Mock 返回空列表
        when(workflowEngine.findWorkflows(eq(TEST_TENANT_ID), eq(null)))
                .thenReturn(Collections.emptyList());

        Result<List<ExecutableWorkflow>> result = flowManagementService.findWorkflows(TEST_TENANT_ID, null);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEmpty();
    }

    @Test
    @DisplayName("复杂流程测试")
    void complexFlowTest() {
        // 创建复杂流程
        ExecutableWorkflow complexWorkflow = createComplexTestWorkflow();
        
        // Mock 发布
        Deployment successDeployment = new Deployment();
        successDeployment.setWorkflowId(new WorkflowId("complex-workflow-id"));
        when(workflowEngine.deployWorkflow(complexWorkflow)).thenReturn(successDeployment);
        
        Result<ExecutableWorkflow> deployResult = flowManagementService.deployFlow(complexWorkflow);
        log.info("复杂流程发布结果：{}", deployResult);
        
        assertThat(deployResult.isSuccess()).isTrue();
        assertThat(deployResult.getData().getActivities()).hasSize(5); // start + 3 logs + end
        assertThat(deployResult.getData().getVariables()).hasSize(3);
        
        // Mock 获取
        when(workflowEngine.findWorkflows(eq(TEST_TENANT_ID), any(WorkflowQuery.class)))
                .thenReturn(Arrays.asList(complexWorkflow));
        
        Result<ExecutableWorkflow> getResult = flowManagementService.getFlow(
                TEST_TENANT_ID, "complex-integration-test-flow");
        
        assertThat(getResult.isSuccess()).isTrue();
        assertThat(getResult.getData().getName()).isEqualTo("复杂集成测试流程");
    }

    @Test
    @DisplayName("批量查找流程测试")
    void batchFindFlowsTest() {
        // 创建多个测试流程
        ExecutableWorkflow workflow1 = createBatchTestWorkflow(1);
        ExecutableWorkflow workflow2 = createBatchTestWorkflow(2);
        ExecutableWorkflow workflow3 = createBatchTestWorkflow(3);
        
        List<ExecutableWorkflow> allWorkflows = Arrays.asList(workflow1, workflow2, workflow3);
        
        // Mock 查找所有流程
        when(workflowEngine.findWorkflows(eq(TEST_TENANT_ID), any(WorkflowQuery.class)))
                .thenReturn(allWorkflows);
        
        WorkflowQuery query = new WorkflowQuery();
        query.tenantId(TEST_TENANT_ID);

        Result<List<ExecutableWorkflow>> result = flowManagementService.findWorkflows(TEST_TENANT_ID, query);

        // 验证包含我们创建的流程
        assertThat(result.isSuccess()).isTrue();
        long batchTestCount = result.getData().stream()
                .filter(w -> w.getName().startsWith("批量测试流程"))
                .count();

        assertThat(batchTestCount).isEqualTo(3);
    }

    @Test
    @DisplayName("启动工作流测试")
    void startWorkflowTest() {
        // 创建并发布测试流程
        ExecutableWorkflow workflow = createTestWorkflow();

        // Mock 发布流程
        Deployment successDeployment = new Deployment();
        successDeployment.setWorkflowId(new WorkflowId("test-workflow-id"));
        when(workflowEngine.deployWorkflow(workflow)).thenReturn(successDeployment);

        Result<ExecutableWorkflow> deployResult = flowManagementService.deployFlow(workflow);
        assertThat(deployResult.isSuccess()).isTrue();

        // 创建触发实例
        TriggerInstance triggerInstance = new TriggerInstance(TEST_TENANT_ID);
        triggerInstance.workflowId(successDeployment.getWorkflowId());
        triggerInstance.data("testVar", "测试值");

        // Mock 启动工作流实例
        WorkflowInstance workflowInstance = new WorkflowInstance();
        workflowInstance.setId(new WorkflowInstanceId("test-instance-id"));
        workflowInstance.setWorkflowId(successDeployment.getWorkflowId());
        workflowInstance.setTenantId(TEST_TENANT_ID);

        when(workflowEngine.start(triggerInstance)).thenReturn(workflowInstance);

        // 启动工作流
        Result<WorkflowInstance> startResult = flowManagementService.start(triggerInstance);
        log.info("启动工作流结果：{}", startResult);

        assertThat(startResult.isSuccess()).isTrue();
        assertThat(startResult.getData()).isNotNull();
        assertThat(startResult.getData().getId()).isNotNull();
        assertThat(startResult.getData().getTenantId()).isEqualTo(TEST_TENANT_ID);
        assertThat(startResult.getMessage()).isEqualTo("Workflow instance started successfully");
    }

    @Test
    @DisplayName("启动工作流参数验证测试")
    void startWorkflowValidationTest() {
        // 测试空触发实例
        Result<WorkflowInstance> result1 = flowManagementService.start(null);
        assertThat(result1.isFailed()).isTrue();
        assertThat(result1.getMessage()).contains("Trigger instance cannot be null");

        // 测试空租户ID
        TriggerInstance emptyTenantTrigger = new TriggerInstance("");
        emptyTenantTrigger.workflowId(new WorkflowId("test-id"));
        Result<WorkflowInstance> result2 = flowManagementService.start(emptyTenantTrigger);
        assertThat(result2.isFailed()).isTrue();
        assertThat(result2.getMessage()).contains("Tenant ID cannot be empty");

        // 测试空工作流ID
        TriggerInstance emptyWorkflowTrigger = new TriggerInstance(TEST_TENANT_ID);
        Result<WorkflowInstance> result3 = flowManagementService.start(emptyWorkflowTrigger);
        assertThat(result3.isFailed()).isTrue();
        assertThat(result3.getMessage()).contains("Either workflow ID or source workflow ID must be provided");
    }

    /**
     * 创建测试用的工作流
     */
    private ExecutableWorkflow createTestWorkflow() {
        ExecutableWorkflow workflow = new ExecutableWorkflow();
        workflow.setName("集成测试流程");
        workflow.setDescription("用于集成测试的工作流");
        workflow.setTenantId(TEST_TENANT_ID);
        workflow.setSourceWorkflowId("integration-test-flow");
        
        // 添加变量
        workflow.variable("testVar", new TextType());
        
        // 添加活动
        workflow.activity("start", new StartEvent().transitionToNext());
        workflow.activity("log", new LogAction()
                .logTemplate("集成测试日志：{{testVar}}")
                .transitionToNext());
        workflow.activity("end", new EndEvent());
        
        return workflow;
    }

    /**
     * 创建复杂的测试工作流
     */
    private ExecutableWorkflow createComplexTestWorkflow() {
        ExecutableWorkflow workflow = new ExecutableWorkflow();
        workflow.setName("复杂集成测试流程");
        workflow.setDescription("包含多个活动的复杂工作流");
        workflow.setTenantId(TEST_TENANT_ID);
        workflow.setSourceWorkflowId("complex-integration-test-flow");
        
        // 添加多个变量
        workflow.variable("input1", new TextType());
        workflow.variable("input2", new TextType());
        workflow.variable("output", new TextType());
        
        // 添加多个活动
        workflow.activity("start", new StartEvent().transitionToNext());
        
        workflow.activity("log1", new LogAction()
                .logTemplate("第一步：处理输入 {{input1}}")
                .transitionToNext());
        
        workflow.activity("log2", new LogAction()
                .logTemplate("第二步：处理输入 {{input2}}")
                .transitionToNext());
        
        workflow.activity("log3", new LogAction()
                .logTemplate("第三步：生成输出 {{output}}")
                .transitionToNext());
        
        workflow.activity("end", new EndEvent());
        
        return workflow;
    }

    /**
     * 创建批量测试工作流
     */
    private ExecutableWorkflow createBatchTestWorkflow(int index) {
        ExecutableWorkflow workflow = new ExecutableWorkflow();
        workflow.setName("批量测试流程" + index);
        workflow.setDescription("批量测试用流程 " + index);
        workflow.setTenantId(TEST_TENANT_ID);
        workflow.setSourceWorkflowId("batch-test-flow-" + index);
        
        workflow.activity("start", new StartEvent().transitionToNext());
        workflow.activity("end", new EndEvent());
        
        return workflow;
    }
}
