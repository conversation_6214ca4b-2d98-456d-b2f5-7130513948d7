package com.fxiaoke.erpdss.ipaas.flow.service;

import com.effektif.workflow.api.WorkflowEngine;
import com.effektif.workflow.api.model.Deployment;
import com.effektif.workflow.api.model.TriggerInstance;
import com.effektif.workflow.api.model.WorkflowId;
import com.effektif.workflow.api.model.WorkflowInstanceId;
import com.effektif.workflow.api.query.WorkflowQuery;
import com.effektif.workflow.api.workflow.ExecutableWorkflow;
import com.effektif.workflow.api.workflow.ParseIssue;
import com.effektif.workflow.api.workflowinstance.WorkflowInstance;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSBizException;
import com.fxiaoke.erpdss.ipaas.common.exception.IPaaSSystemException;
import com.fxiaoke.erpdss.ipaas.common.module.Result;
import com.fxiaoke.erpdss.ipaas.common.module.ResultCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * FlowManagementServiceImpl 单元测试
 *
 * <AUTHOR> (^_−)☆
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("流程管理服务测试")
class FlowManagementServiceImplTest {

    @Mock
    private WorkflowEngine workflowEngine;

    @InjectMocks
    private FlowManagementServiceImpl flowManagementService;

    private ExecutableWorkflow testWorkflow;
    private Deployment successDeployment;
    private Deployment errorDeployment;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testWorkflow = new ExecutableWorkflow();
        testWorkflow.setName("测试流程");
        testWorkflow.setId(new WorkflowId("test-workflow-id"));

        // 成功的部署结果
        successDeployment = new Deployment();
        successDeployment.setWorkflowId(new WorkflowId("test-workflow-id"));

        // 有错误的部署结果
        errorDeployment = new Deployment();
        errorDeployment.setWorkflowId(new WorkflowId("test-workflow-id"));
        errorDeployment.addIssue(ParseIssue.IssueType.error, null, null, null, "测试错误");
    }

    @Test
    @DisplayName("发布流程 - 成功场景")
    void deployFlow_Success() {
        // Given
        when(workflowEngine.deployWorkflow(testWorkflow)).thenReturn(successDeployment);

        // When
        Result<ExecutableWorkflow> result = flowManagementService.deployFlow(testWorkflow);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo(testWorkflow);
        assertThat(result.getMessage()).isEqualTo("Workflow deployed successfully");
        verify(workflowEngine).deployWorkflow(testWorkflow);
    }

    @Test
    @DisplayName("发布流程 - 工作流为空")
    void deployFlow_NullWorkflow() {
        // When
        Result<ExecutableWorkflow> result = flowManagementService.deployFlow(null);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Workflow definition cannot be null");
        verify(workflowEngine, never()).deployWorkflow(any());
    }

    @Test
    @DisplayName("发布流程 - 工作流名称为空")
    void deployFlow_EmptyWorkflowName() {
        // Given
        testWorkflow.setName("");

        // When
        Result<ExecutableWorkflow> result = flowManagementService.deployFlow(testWorkflow);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Workflow name cannot be empty");
        verify(workflowEngine, never()).deployWorkflow(any());
    }

    @Test
    @DisplayName("发布流程 - 部署有错误")
    void deployFlow_DeploymentWithErrors() {
        // Given
        when(workflowEngine.deployWorkflow(testWorkflow)).thenReturn(errorDeployment);

        // When
        Result<ExecutableWorkflow> result = flowManagementService.deployFlow(testWorkflow);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(result.getMessage()).contains("Failed to deploy workflow");
        verify(workflowEngine).deployWorkflow(testWorkflow);
    }

    @Test
    @DisplayName("发布流程 - 引擎异常")
    void deployFlow_EngineException() {
        // Given
        when(workflowEngine.deployWorkflow(testWorkflow))
                .thenThrow(new RuntimeException("引擎异常"));

        // When & Then
        assertThatThrownBy(() -> flowManagementService.deployFlow(testWorkflow))
                .isInstanceOf(IPaaSSystemException.class)
                .hasMessage("System exception occurred while deploying workflow");
        verify(workflowEngine).deployWorkflow(testWorkflow);
    }

    @Test
    @DisplayName("更新流程 - 成功场景")
    void updateFlow_Success() {
        // Given
        when(workflowEngine.updateWorkflow(testWorkflow)).thenReturn(successDeployment);

        // When
        Result<ExecutableWorkflow> result = flowManagementService.updateFlow(testWorkflow);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo(testWorkflow);
        assertThat(result.getMessage()).isEqualTo("Workflow updated successfully");
        verify(workflowEngine).updateWorkflow(testWorkflow);
    }

    @Test
    @DisplayName("更新流程 - 工作流为空")
    void updateFlow_NullWorkflow() {
        // When
        Result<ExecutableWorkflow> result = flowManagementService.updateFlow(null);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Workflow definition cannot be null");
        verify(workflowEngine, never()).updateWorkflow(any());
    }

    @Test
    @DisplayName("更新流程 - 工作流ID为空")
    void updateFlow_NullWorkflowId() {
        // Given
        testWorkflow.setId(null);

        // When
        Result<ExecutableWorkflow> result = flowManagementService.updateFlow(testWorkflow);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Workflow ID cannot be null");
        verify(workflowEngine, never()).updateWorkflow(any());
    }

    @Test
    @DisplayName("查找流程列表 - 成功场景")
    void findWorkflows_Success() {
        // Given
        String tenantId = "test-tenant";
        WorkflowQuery query = new WorkflowQuery();
        List<ExecutableWorkflow> workflows = Arrays.asList(testWorkflow);
        when(workflowEngine.findWorkflows(tenantId, query)).thenReturn(workflows);

        // When
        Result<List<ExecutableWorkflow>> result = flowManagementService.findWorkflows(tenantId, query);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).hasSize(1);
        assertThat(result.getData().get(0)).isEqualTo(testWorkflow);
        assertThat(result.getMessage()).isEqualTo("Workflows found successfully");
        verify(workflowEngine).findWorkflows(tenantId, query);
    }

    @Test
    @DisplayName("查找流程列表 - 租户ID为空")
    void findWorkflows_EmptyTenantId() {
        // When
        Result<List<ExecutableWorkflow>> result = flowManagementService.findWorkflows("", new WorkflowQuery());

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Tenant ID cannot be empty");
        verify(workflowEngine, never()).findWorkflows(any(), any());
    }

    @Test
    @DisplayName("查找流程列表 - 返回空结果")
    void findWorkflows_EmptyResult() {
        // Given
        String tenantId = "test-tenant";
        WorkflowQuery query = new WorkflowQuery();
        when(workflowEngine.findWorkflows(tenantId, query)).thenReturn(null);

        // When
        Result<List<ExecutableWorkflow>> result = flowManagementService.findWorkflows(tenantId, query);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEmpty();
        assertThat(result.getMessage()).isEqualTo("No workflows found");
        verify(workflowEngine).findWorkflows(tenantId, query);
    }

    @Test
    @DisplayName("获取流程 - 成功场景")
    void getFlow_Success() {
        // Given
        String tenantId = "test-tenant";
        String workflowSource = "test-source";
        List<ExecutableWorkflow> workflows = Arrays.asList(testWorkflow);
        when(workflowEngine.findWorkflows(eq(tenantId), any(WorkflowQuery.class)))
                .thenReturn(workflows);

        // When
        Result<ExecutableWorkflow> result = flowManagementService.getFlow(tenantId, workflowSource);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo(testWorkflow);
        assertThat(result.getMessage()).isEqualTo("Workflow retrieved successfully");
        verify(workflowEngine).findWorkflows(eq(tenantId), any(WorkflowQuery.class));
    }

    @Test
    @DisplayName("获取流程 - 租户ID为空")
    void getFlow_EmptyTenantId() {
        // When
        Result<ExecutableWorkflow> result = flowManagementService.getFlow("", "test-source");

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Tenant ID cannot be empty");
        verify(workflowEngine, never()).findWorkflows(any(), any());
    }

    @Test
    @DisplayName("获取流程 - 工作流源为空")
    void getFlow_EmptyWorkflowSource() {
        // When
        Result<ExecutableWorkflow> result = flowManagementService.getFlow("test-tenant", "");

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Workflow source cannot be empty");
        verify(workflowEngine, never()).findWorkflows(any(), any());
    }

    @Test
    @DisplayName("获取流程 - 未找到流程")
    void getFlow_NotFound() {
        // Given
        String tenantId = "test-tenant";
        String workflowSource = "test-source";
        when(workflowEngine.findWorkflows(eq(tenantId), any(WorkflowQuery.class)))
                .thenReturn(Collections.emptyList());

        // When
        Result<ExecutableWorkflow> result = flowManagementService.getFlow(tenantId, workflowSource);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(result.getMessage()).isEqualTo("Workflow not found");
        verify(workflowEngine).findWorkflows(eq(tenantId), any(WorkflowQuery.class));
    }

    @Test
    @DisplayName("启动工作流 - 成功场景")
    void start_Success() {
        // Given
        TriggerInstance triggerInstance = new TriggerInstance("test-tenant");
        triggerInstance.workflowId(new WorkflowId("test-workflow-id"));

        WorkflowInstance workflowInstance = new WorkflowInstance();
        workflowInstance.setId(new WorkflowInstanceId("test-instance-id"));
        workflowInstance.setWorkflowId(new WorkflowId("test-workflow-id"));
        workflowInstance.setTenantId("test-tenant");

        when(workflowEngine.start(triggerInstance)).thenReturn(workflowInstance);

        // When
        Result<WorkflowInstance> result = flowManagementService.start(triggerInstance);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo(workflowInstance);
        assertThat(result.getMessage()).isEqualTo("Workflow instance started successfully");
        verify(workflowEngine).start(triggerInstance);
    }

    @Test
    @DisplayName("启动工作流 - 触发实例为空")
    void start_NullTriggerInstance() {
        // When
        Result<WorkflowInstance> result = flowManagementService.start(null);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Trigger instance cannot be null");
        verify(workflowEngine, never()).start(any());
    }

    @Test
    @DisplayName("启动工作流 - 租户ID为空")
    void start_EmptyTenantId() {
        // Given
        TriggerInstance triggerInstance = new TriggerInstance("");
        triggerInstance.workflowId(new WorkflowId("test-workflow-id"));

        // When
        Result<WorkflowInstance> result = flowManagementService.start(triggerInstance);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Tenant ID cannot be empty");
        verify(workflowEngine, never()).start(any());
    }

    @Test
    @DisplayName("启动工作流 - 工作流ID和源工作流ID都为空")
    void start_EmptyWorkflowIds() {
        // Given
        TriggerInstance triggerInstance = new TriggerInstance("test-tenant");

        // When
        Result<WorkflowInstance> result = flowManagementService.start(triggerInstance);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.PARAM_ILLEGAL.getCode());
        assertThat(result.getMessage()).isEqualTo("Either workflow ID or source workflow ID must be provided");
        verify(workflowEngine, never()).start(any());
    }

    @Test
    @DisplayName("启动工作流 - 引擎返回空实例")
    void start_NullWorkflowInstance() {
        // Given
        TriggerInstance triggerInstance = new TriggerInstance("test-tenant");
        triggerInstance.workflowId(new WorkflowId("test-workflow-id"));

        when(workflowEngine.start(triggerInstance)).thenReturn(null);

        // When
        Result<WorkflowInstance> result = flowManagementService.start(triggerInstance);

        // Then
        assertThat(result.isFailed()).isTrue();
        assertThat(result.getCode()).isEqualTo(ResultCode.BIZ_ERROR.getCode());
        assertThat(result.getMessage()).isEqualTo("Failed to start workflow instance");
        verify(workflowEngine).start(triggerInstance);
    }

    @Test
    @DisplayName("启动工作流 - 引擎异常")
    void start_EngineException() {
        // Given
        TriggerInstance triggerInstance = new TriggerInstance("test-tenant");
        triggerInstance.workflowId(new WorkflowId("test-workflow-id"));

        when(workflowEngine.start(triggerInstance))
                .thenThrow(new RuntimeException("Engine exception"));

        // When & Then
        assertThatThrownBy(() -> flowManagementService.start(triggerInstance))
                .isInstanceOf(IPaaSSystemException.class)
                .hasMessage("System exception occurred while starting workflow");
        verify(workflowEngine).start(triggerInstance);
    }

    @Test
    @DisplayName("启动工作流 - 使用源工作流ID")
    void start_WithSourceWorkflowId() {
        // Given
        TriggerInstance triggerInstance = new TriggerInstance("test-tenant");
        triggerInstance.sourceWorkflowId("source-workflow-id");

        WorkflowInstance workflowInstance = new WorkflowInstance();
        workflowInstance.setId(new WorkflowInstanceId("test-instance-id"));
        workflowInstance.setTenantId("test-tenant");

        when(workflowEngine.start(triggerInstance)).thenReturn(workflowInstance);

        // When
        Result<WorkflowInstance> result = flowManagementService.start(triggerInstance);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo(workflowInstance);
        verify(workflowEngine).start(triggerInstance);
    }
}
