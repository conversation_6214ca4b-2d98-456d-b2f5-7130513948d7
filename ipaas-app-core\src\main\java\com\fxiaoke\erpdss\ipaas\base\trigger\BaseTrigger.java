package com.fxiaoke.erpdss.ipaas.base.trigger;

import com.fxiaoke.erpdss.ipaas.flow.TriggerResult;
import com.fxiaoke.erpdss.ipaas.flow.TriggerService;
import com.fxiaoke.erpdss.ipaas.flow.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * 基础触发器抽象类
 * 所有具体触发器都应该继承此类
 * 
 * <AUTHOR> (^_−)☆
 */
public abstract class BaseTrigger {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    protected TriggerService triggerService;

    /**
     * 获取触发器类型
     */
    public abstract String getTriggerType();

    /**
     * 获取触发器名称
     */
    public abstract String getTriggerName();

    /**
     * 获取触发器描述
     */
    public abstract String getTriggerDescription();

    /**
     * 验证触发器配置
     */
    public abstract ValidationResult validateConfig(Map<String, Object> config);

    /**
     * 启动触发器
     */
    public abstract void start(String triggerId, Map<String, Object> config);

    /**
     * 停止触发器
     */
    public abstract void stop(String triggerId);

    /**
     * 触发流程执行
     * 这是核心方法，当触发条件满足时调用
     */
    protected TriggerResult triggerFlow(String triggerId, Map<String, Object> triggerData) {
        try {
            logger.info("Triggering flow for trigger: {}, data: {}", triggerId, triggerData);
            
            // 添加触发器类型信息
            triggerData.put("triggerType", getTriggerType());
            triggerData.put("triggerName", getTriggerName());
            triggerData.put("timestamp", System.currentTimeMillis());
            
            return triggerService.triggerFlow(triggerId, triggerData);
            
        } catch (Exception e) {
            logger.error("Failed to trigger flow for trigger: " + triggerId, e);
            return TriggerResult.failure(triggerId, "Failed to trigger flow: " + e.getMessage());
        }
    }

    /**
     * 处理触发器错误
     */
    protected void handleError(String triggerId, String operation, Exception e) {
        logger.error("Error in trigger {} during {}: {}", triggerId, operation, e.getMessage(), e);
    }

    /**
     * 获取配置值
     */
    protected Object getConfigValue(Map<String, Object> config, String key, Object defaultValue) {
        return config != null ? config.getOrDefault(key, defaultValue) : defaultValue;
    }

    /**
     * 获取必需的配置值
     */
    protected Object getRequiredConfigValue(Map<String, Object> config, String key) throws IllegalArgumentException {
        if (config == null || !config.containsKey(key)) {
            throw new IllegalArgumentException("Required configuration key '" + key + "' is missing");
        }
        Object value = config.get(key);
        if (value == null) {
            throw new IllegalArgumentException("Required configuration key '" + key + "' cannot be null");
        }
        return value;
    }

    /**
     * 验证基础配置
     */
    protected ValidationResult validateBaseConfig(Map<String, Object> config) {
        ValidationResult result = new ValidationResult();
        
        if (config == null) {
            result.addError("Configuration cannot be null");
            return result;
        }
        
        // 基础验证逻辑
        if (!config.containsKey("enabled")) {
            result.addWarning("'enabled' flag not specified, defaulting to true");
        }
        
        return result;
    }
}
