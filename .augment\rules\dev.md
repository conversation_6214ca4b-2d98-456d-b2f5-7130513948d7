---
type: "agent_requested"
description: "service interface 定义"
---
# iPaaS 项目开发规则

## Service Interface 定义规则
- 必须返回 `com.fxiaoke.erpdss.ipaas.common.module.Result`

## 异常处理规范
- 捕获未知类型异常必须打印堆栈
- 捕获异常后，如果不是可以忽略的，或者继续向上抛出异常的，都必须打印日志方便定位
- 优先使用项目定义的业务异常类（如 `IPaaSBizException`、`IPaaSSystemException`）
- 异常信息应该清晰描述问题和上下文
- 使用国际化消息

## 日志记录规范
- 使用 SLF4J 进行日志记录
- 合理选择日志级别
- 避免在循环中打印大量日志
- 使用占位符而不是字符串拼接
- 日志使用英文描述

## 代码示例

### 推荐的异常处理
```java
try {
    // 业务逻辑
} catch (JsonProcessingException e) {
    log.error("json process error: {}", data, e);
    throw new IPaaSSystemException("数据序列化失败", e);
}
```

### 推荐的日志记录
```java
log.info("user{}login success，IP地址：{}", username, ipAddress);
log.error("An error occurred while processing order {}", orderId, e);
```

### 推荐的异常抛出
```java
if (user == null) {
    throw new IPaaSBizException(ResultCode.PARAM_ILLEGAL.getI18nMsg());
}
```

